#!/bin/bash

echo "🔍 Final check for Chinese characters in src/store directory..."
echo "============================================================"

total_files=0
files_with_chinese=0

# Check each file individually
for file in src/store/*.ts src/store/modules/*.ts; do
    if [ -f "$file" ]; then
        total_files=$((total_files + 1))
        echo -n "Checking $file... "
        
        # Look for specific Chinese characters that might remain
        if grep -E "(中文|汉字|简体|繁体|中国|用户|密码|登录|注册|设置|配置|管理|系统|数据|文件|图片|视频|音频|下载|上传|删除|编辑|保存|取消|确定|提交|重置|搜索|查询|添加|修改|详情|列表|页面|组件|模块|服务|接口|请求|响应|成功|失败|错误|警告|提示|信息|通知|消息|状态|类型|名称|标题|内容|描述|备注|时间|日期|开始|结束|创建|更新|发布|草稿|审核|通过|拒绝|启用|禁用|激活|停用|正常|异常|在线|离线|连接|断开|网络|服务器|客户端|前端|后端|数据库|缓存|存储|备份|恢复|导入|导出|打印|预览|刷新|重新|加载|提交|发送|接收|同步|异步|并发|串行|队列|任务|作业|调度|定时|延时|超时|重试|失败|成功|完成|进行|等待|暂停|停止|开始|结束|判断|是否|手动|点击|浏览器|窗口|可视|区域|静态|路由|生成|整体|动态|组装|监听|存在|不存在|则删除|清除|缓存|页面|标签|外链|无需|添加|信息|到标签|显示|链接|已经|打开|数量|大于|替换|第一个|当前|已打开|数目|如果|超过|限制)" "$file" >/dev/null 2>&1; then
            files_with_chinese=$((files_with_chinese + 1))
            echo "❌ FOUND CHINESE"
            # Show the problematic lines
            grep -n -E "(中文|汉字|简体|繁体|中国|用户|密码|登录|注册|设置|配置|管理|系统|数据|文件|图片|视频|音频|下载|上传|删除|编辑|保存|取消|确定|提交|重置|搜索|查询|添加|修改|详情|列表|页面|组件|模块|服务|接口|请求|响应|成功|失败|错误|警告|提示|信息|通知|消息|状态|类型|名称|标题|内容|描述|备注|时间|日期|开始|结束|创建|更新|发布|草稿|审核|通过|拒绝|启用|禁用|激活|停用|正常|异常|在线|离线|连接|断开|网络|服务器|客户端|前端|后端|数据库|缓存|存储|备份|恢复|导入|导出|打印|预览|刷新|重新|加载|提交|发送|接收|同步|异步|并发|串行|队列|任务|作业|调度|定时|延时|超时|重试|失败|成功|完成|进行|等待|暂停|停止|开始|结束|判断|是否|手动|点击|浏览器|窗口|可视|区域|静态|路由|生成|整体|动态|组装|监听|存在|不存在|则删除|清除|缓存|页面|标签|外链|无需|添加|信息|到标签|显示|链接|已经|打开|数量|大于|替换|第一个|当前|已打开|数目|如果|超过|限制)" "$file" | head -3
            echo "---"
        else
            echo "✅ CLEAN"
        fi
    fi
done

echo ""
echo "📊 FINAL RESULTS:"
echo "============================================================"
echo "✅ Total files checked: $total_files"
echo "❌ Files with Chinese: $files_with_chinese"
echo "✅ Files clean: $((total_files - files_with_chinese))"

if [ $files_with_chinese -eq 0 ]; then
    echo ""
    echo "🎉🎉🎉 SUCCESS! 🎉🎉🎉"
    echo "All store files are now free of Chinese characters!"
    echo "The store directory is fully internationalized."
else
    echo ""
    echo "⚠️  WARNING: $files_with_chinese files still contain Chinese characters"
    echo "Manual review may be needed for these files."
fi

echo ""
echo "📁 Store directory structure:"
echo "src/store/"
echo "├── index.ts"
echo "├── types.ts"
echo "├── utils.ts"
echo "└── modules/"
echo "    ├── app.ts"
echo "    ├── epTheme.ts"
echo "    ├── language.ts"
echo "    ├── multiTags.ts"
echo "    ├── permission.ts"
echo "    ├── settings.ts"
echo "    └── user.ts"
