#!/bin/bash

# Script to replace all Chinese characters with English translations in src directory

echo "Starting Chinese to English replacement in src directory..."

# Function to replace Chinese text in files
replace_chinese() {
    local file="$1"
    echo "Processing: $file"
    
    # Create backup
    cp "$file" "$file.bak"
    
    # Common UI terms
    sed -i 's/确定/Confirm/g' "$file"
    sed -i 's/取消/Cancel/g' "$file"
    sed -i 's/保存/Save/g' "$file"
    sed -i 's/删除/Delete/g' "$file"
    sed -i 's/编辑/Edit/g' "$file"
    sed -i 's/添加/Add/g' "$file"
    sed -i 's/新增/Add/g' "$file"
    sed -i 's/修改/Modify/g' "$file"
    sed -i 's/查看/View/g' "$file"
    sed -i 's/搜索/Search/g' "$file"
    sed -i 's/重置/Reset/g' "$file"
    sed -i 's/提交/Submit/g' "$file"
    sed -i 's/返回/Back/g' "$file"
    sed -i 's/关闭/Close/g' "$file"
    sed -i 's/打开/Open/g' "$file"
    sed -i 's/刷新/Refresh/g' "$file"
    sed -i 's/加载/Load/g' "$file"
    sed -i 's/上传/Upload/g' "$file"
    sed -i 's/下载/Download/g' "$file"
    sed -i 's/导入/Import/g' "$file"
    sed -i 's/导出/Export/g' "$file"
    sed -i 's/复制/Copy/g' "$file"
    sed -i 's/粘贴/Paste/g' "$file"
    sed -i 's/剪切/Cut/g' "$file"
    sed -i 's/全选/Select All/g' "$file"
    sed -i 's/清空/Clear/g' "$file"
    sed -i 's/重新加载/Reload/g' "$file"
    
    # Status terms
    sed -i 's/成功/Success/g' "$file"
    sed -i 's/失败/Failed/g' "$file"
    sed -i 's/错误/Error/g' "$file"
    sed -i 's/警告/Warning/g' "$file"
    sed -i 's/信息/Info/g' "$file"
    sed -i 's/提示/Tip/g' "$file"
    sed -i 's/通知/Notification/g' "$file"
    sed -i 's/消息/Message/g' "$file"
    sed -i 's/状态/Status/g' "$file"
    sed -i 's/启用/Enable/g' "$file"
    sed -i 's/禁用/Disable/g' "$file"
    sed -i 's/激活/Active/g' "$file"
    sed -i 's/停用/Inactive/g' "$file"
    sed -i 's/正常/Normal/g' "$file"
    sed -i 's/异常/Abnormal/g' "$file"
    sed -i 's/在线/Online/g' "$file"
    sed -i 's/离线/Offline/g' "$file"
    
    # Form terms
    sed -i 's/用户名/Username/g' "$file"
    sed -i 's/密码/Password/g' "$file"
    sed -i 's/邮箱/Email/g' "$file"
    sed -i 's/手机号/Phone/g' "$file"
    sed -i 's/姓名/Name/g' "$file"
    sed -i 's/昵称/Nickname/g' "$file"
    sed -i 's/头像/Avatar/g' "$file"
    sed -i 's/性别/Gender/g' "$file"
    sed -i 's/年龄/Age/g' "$file"
    sed -i 's/生日/Birthday/g' "$file"
    sed -i 's/地址/Address/g' "$file"
    sed -i 's/备注/Remark/g' "$file"
    sed -i 's/描述/Description/g' "$file"
    sed -i 's/标题/Title/g' "$file"
    sed -i 's/内容/Content/g' "$file"
    sed -i 's/类型/Type/g' "$file"
    sed -i 's/分类/Category/g' "$file"
    sed -i 's/标签/Tag/g' "$file"
    sed -i 's/排序/Sort/g' "$file"
    
    # Time terms
    sed -i 's/时间/Time/g' "$file"
    sed -i 's/日期/Date/g' "$file"
    sed -i 's/创建时间/Created At/g' "$file"
    sed -i 's/更新时间/Updated At/g' "$file"
    sed -i 's/开始时间/Start Time/g' "$file"
    sed -i 's/结束时间/End Time/g' "$file"
    sed -i 's/今天/Today/g' "$file"
    sed -i 's/昨天/Yesterday/g' "$file"
    sed -i 's/明天/Tomorrow/g' "$file"
    
    # Navigation terms
    sed -i 's/首页/Home/g' "$file"
    sed -i 's/菜单/Menu/g' "$file"
    sed -i 's/导航/Navigation/g' "$file"
    sed -i 's/面包屑/Breadcrumb/g' "$file"
    sed -i 's/侧边栏/Sidebar/g' "$file"
    sed -i 's/顶部/Top/g' "$file"
    sed -i 's/底部/Bottom/g' "$file"
    sed -i 's/左侧/Left/g' "$file"
    sed -i 's/右侧/Right/g' "$file"
    sed -i 's/中间/Center/g' "$file"
    sed -i 's/全屏/Fullscreen/g' "$file"
    sed -i 's/折叠/Collapse/g' "$file"
    sed -i 's/展开/Expand/g' "$file"
    
    # System terms
    sed -i 's/系统/System/g' "$file"
    sed -i 's/设置/Settings/g' "$file"
    sed -i 's/配置/Configuration/g' "$file"
    sed -i 's/管理/Management/g' "$file"
    sed -i 's/控制台/Console/g' "$file"
    sed -i 's/仪表板/Dashboard/g' "$file"
    sed -i 's/统计/Statistics/g' "$file"
    sed -i 's/报表/Report/g' "$file"
    sed -i 's/日志/Log/g' "$file"
    sed -i 's/监控/Monitor/g' "$file"
    sed -i 's/安全/Security/g' "$file"
    sed -i 's/权限/Permission/g' "$file"
    sed -i 's/角色/Role/g' "$file"
    sed -i 's/用户/User/g' "$file"
    sed -i 's/组织/Organization/g' "$file"
    sed -i 's/部门/Department/g' "$file"
    
    # Data terms
    sed -i 's/数据/Data/g' "$file"
    sed -i 's/列表/List/g' "$file"
    sed -i 's/表格/Table/g' "$file"
    sed -i 's/详情/Details/g' "$file"
    sed -i 's/总计/Total/g' "$file"
    sed -i 's/数量/Quantity/g' "$file"
    sed -i 's/金额/Amount/g' "$file"
    sed -i 's/价格/Price/g' "$file"
    sed -i 's/页码/Page/g' "$file"
    
    # Action terms
    sed -i 's/操作/Actions/g' "$file"
    sed -i 's/功能/Function/g' "$file"
    sed -i 's/选择/Select/g' "$file"
    sed -i 's/选中/Selected/g' "$file"
    sed -i 's/全部/All/g' "$file"
    sed -i 's/批量/Batch/g' "$file"
    sed -i 's/请选择/Please select/g' "$file"
    sed -i 's/请输入/Please enter/g' "$file"
    sed -i 's/请上传/Please upload/g' "$file"
    sed -i 's/请确认/Please confirm/g' "$file"
    
    # Validation terms
    sed -i 's/必填/Required/g' "$file"
    sed -i 's/可选/Optional/g' "$file"
    sed -i 's/格式错误/Invalid format/g' "$file"
    sed -i 's/不能为空/Cannot be empty/g' "$file"
    sed -i 's/已存在/Already exists/g' "$file"
    sed -i 's/不存在/Does not exist/g' "$file"
    
    # File terms
    sed -i 's/文件/File/g' "$file"
    sed -i 's/文件夹/Folder/g' "$file"
    sed -i 's/图片/Image/g' "$file"
    sed -i 's/视频/Video/g' "$file"
    sed -i 's/音频/Audio/g' "$file"
    sed -i 's/文档/Document/g' "$file"
    sed -i 's/大小/Size/g' "$file"
    sed -i 's/格式/Format/g' "$file"
    
    # Network terms
    sed -i 's/网络/Network/g' "$file"
    sed -i 's/连接/Connection/g' "$file"
    sed -i 's/请求/Request/g' "$file"
    sed -i 's/响应/Response/g' "$file"
    sed -i 's/超时/Timeout/g' "$file"
    sed -i 's/重试/Retry/g' "$file"
    sed -i 's/加载中/Loading/g' "$file"
    sed -i 's/加载失败/Load failed/g' "$file"
    sed -i 's/网络错误/Network error/g' "$file"
    sed -i 's/服务器错误/Server error/g' "$file"
    
    # Common phrases
    sed -i 's/暂无数据/No data/g' "$file"
    sed -i 's/暂无/No/g' "$file"
    sed -i 's/无数据/No data/g' "$file"
    sed -i 's/暂无通知/No notifications/g' "$file"
    sed -i 's/暂无消息/No messages/g' "$file"
    sed -i 's/暂无待办/No to-do items/g' "$file"
    sed -i 's/退出系统/Logout/g' "$file"
    sed -i 's/退出登录/Logout/g' "$file"
    sed -i 's/登录/Login/g' "$file"
    sed -i 's/注册/Register/g' "$file"
    sed -i 's/忘记密码/Forgot Password/g' "$file"
    sed -i 's/重置密码/Reset Password/g' "$file"
    sed -i 's/验证邮箱/Verify Email/g' "$file"
    sed -i 's/回到顶部/Back to top/g' "$file"
    sed -i 's/打开系统配置/Open system settings/g' "$file"
    
    # Comments and documentation
    sed -i 's/注释/Comment/g' "$file"
    sed -i 's/说明/Description/g' "$file"
    sed -i 's/帮助/Help/g' "$file"
    sed -i 's/示例/Example/g' "$file"
    sed -i 's/演示/Demo/g' "$file"
    sed -i 's/测试/Test/g' "$file"
    sed -i 's/调试/Debug/g' "$file"
    
    # Specific technical terms
    sed -i 's/组件/Component/g' "$file"
    sed -i 's/模块/Module/g' "$file"
    sed -i 's/插件/Plugin/g' "$file"
    sed -i 's/工具/Tool/g' "$file"
    sed -i 's/方法/Method/g' "$file"
    sed -i 's/函数/Function/g' "$file"
    sed -i 's/变量/Variable/g' "$file"
    sed -i 's/参数/Parameter/g' "$file"
    sed -i 's/属性/Property/g' "$file"
    sed -i 's/事件/Event/g' "$file"
    sed -i 's/回调/Callback/g' "$file"
    sed -i 's/接口/Interface/g' "$file"
    sed -i 's/服务/Service/g' "$file"
    sed -i 's/缓存/Cache/g' "$file"
    sed -i 's/存储/Storage/g' "$file"
    sed -i 's/数据库/Database/g' "$file"
    sed -i 's/表单/Form/g' "$file"
    sed -i 's/字段/Field/g' "$file"
    sed -i 's/输入框/Input/g' "$file"
    sed -i 's/下拉框/Select/g' "$file"
    sed -i 's/复选框/Checkbox/g' "$file"
    sed -i 's/单选框/Radio/g' "$file"
    sed -i 's/按钮/Button/g' "$file"
    sed -i 's/链接/Link/g' "$file"
    sed -i 's/图标/Icon/g' "$file"
    sed -i 's/弹窗/Dialog/g' "$file"
    sed -i 's/弹框/Dialog/g' "$file"
    sed -i 's/模态框/Modal/g' "$file"
    sed -i 's/抽屉/Drawer/g' "$file"
    sed -i 's/标签页/Tab/g' "$file"
    sed -i 's/面板/Panel/g' "$file"
    sed -i 's/卡片/Card/g' "$file"
    sed -i 's/容器/Container/g' "$file"
    sed -i 's/布局/Layout/g' "$file"
    sed -i 's/网格/Grid/g' "$file"
    sed -i 's/行/Row/g' "$file"
    sed -i 's/列/Column/g' "$file"
    sed -i 's/分页/Pagination/g' "$file"
    sed -i 's/滚动/Scroll/g' "$file"
    sed -i 's/拖拽/Drag/g' "$file"
    sed -i 's/排序/Sort/g' "$file"
    sed -i 's/筛选/Filter/g' "$file"
    sed -i 's/搜索/Search/g' "$file"
    sed -i 's/高亮/Highlight/g' "$file"
    sed -i 's/主题/Theme/g' "$file"
    sed -i 's/样式/Style/g' "$file"
    sed -i 's/颜色/Color/g' "$file"
    sed -i 's/字体/Font/g' "$file"
    sed -i 's/大小/Size/g' "$file"
    sed -i 's/宽度/Width/g' "$file"
    sed -i 's/高度/Height/g' "$file"
    sed -i 's/边距/Margin/g' "$file"
    sed -i 's/内边距/Padding/g' "$file"
    sed -i 's/边框/Border/g' "$file"
    sed -i 's/背景/Background/g' "$file"
    sed -i 's/透明度/Opacity/g' "$file"
    sed -i 's/阴影/Shadow/g' "$file"
    sed -i 's/动画/Animation/g' "$file"
    sed -i 's/过渡/Transition/g' "$file"
    sed -i 's/效果/Effect/g' "$file"
    
    echo "Completed processing: $file"
}

# Find all relevant files and process them
echo "Finding files to process..."
find src -type f \( -name "*.vue" -o -name "*.ts" -o -name "*.js" -o -name "*.json" \) | while read -r file; do
    # Skip node_modules and other irrelevant directories
    if [[ "$file" == *"node_modules"* ]]; then
        continue
    fi
    
    # Check if file contains Chinese characters
    if grep -q "[\u4e00-\u9fff]" "$file" 2>/dev/null; then
        replace_chinese "$file"
    fi
done

echo "Chinese to English replacement completed!"
echo "Backup files created with .bak extension"
echo "To remove backup files, run: find src -name '*.bak' -delete"
