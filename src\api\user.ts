import { http } from "@/utils/http";

export type UserResult = {
  success: boolean;
  data: {
    /** Avatar */
    avatar: string;
    /** Username */
    username: string;
    /** Nickname */
    nickname: string;
    /** Current logged-in user's roles */
    roles: Array<string>;
    /** Button-level permissions */
    permissions: Array<string>;
    /** `token` */
    accessToken: string;
    /** Token required for calling the refresh `accessToken` interface */
    refreshToken: string;
    /** Expiration time of `accessToken` (format 'xxxx/xx/xx xx:xx:xx') */
    expires: Date;
  };
};

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** Token required for calling the refresh `accessToken` interface */
    refreshToken: string;
    /** Expiration time of `accessToken` (format 'xxxx/xx/xx xx:xx:xx') */
    expires: Date;
  };
};

/** Login */
export const getLogin = (data?: object) => {
  return http.request<UserResult>("post", "/api/auth/login", { data });
};

/** Refresh `token` */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/api/auth/refresh", { data });
};

/** Logout */
export const logoutUser = () => {
  return http.request("post", "/api/auth/logout");
};
