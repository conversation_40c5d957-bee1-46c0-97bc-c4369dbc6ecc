const Layout = () => import("@/layout/index.vue");

export default {
  path: "/chat/management",
  name: "<PERSON><PERSON>",
  redirect: "/chat",
  component: Layout,
  meta: {
    icon: "ri:robot-2-line",
    title: "Chat Management",
    rank: 4
  },
  children: [
    {
      path: "/chat",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      component: () => import("@/views/chat/index.vue"),
      meta: {
        title: "My Chat",
        showLink: true
      }
    },
    {
      path: "/chat/:uuid?",
      name: "ChatAgentEdit",
      component: () => import("@/views/chat/index.vue"),
      meta: {
        title: "Chat Edit",
        showLink: false,
        activePath: "/chat"
      }
    }
  ]
} satisfies RouteConfigsTable;
