const Layout = () => import("@/layout/index.vue");

export default {
  path: "/language",
  name: "Language",
  component: Layout,
  redirect: "/language/index",
  meta: {
    icon: "ri:global-line",
    title: "Language Management",
    rank: 4
  },
  children: [
    {
      path: "/language/index",
      name: "LanguageIndex",
      component: () => import("@/views/language/index.vue"),
      meta: {
        title: "Language Management",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
