const Layout = () => import("@/layout/index.vue");

export default {
  path: "/organizations/management",
  name: "OrganizationManagement",
  redirect: "/organizations",
  component: Layout,
  meta: {
    icon: "ri:building-line",
    title: "Organization Management",
    rank: 4
  },
  children: [
    {
      path: "/organizations",
      name: "OrganizationList",
      component: () => import("@/views/organization/index.vue"),
      meta: {
        title: "Organizations",
        showLink: true
      }
    },
    {
      path: "/organizations/:slug",
      name: "OrganizationDetail",
      component: () => import("@/views/organization/organization.vue"),
      meta: {
        title: "Organizations",
        showLink: false,
        activePath: "/organizations"
      }
    },
    {
      path: "/organizations/:slug/agent",
      name: "OrganizationAgentList",
      component: () => import("@/views/organization/agent.vue"),
      meta: {
        title: "Organizations",
        showLink: false,
        activePath: "/organizations"
      }
    },
    {
      path: "/organizations/:slug/agents/:agentId",
      name: "OrganizationAgentDetail",
      component: () => import("@/views/organization/agent-detail.vue"),
      meta: {
        title: "Agent Detail",
        showLink: false,
        activePath: "/organizations"
      }
    }
  ]
} satisfies RouteConfigsTable;
