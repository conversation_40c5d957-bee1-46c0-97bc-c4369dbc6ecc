const Layout = () => import("@/layout/index.vue");

export default {
  path: "/translation",
  name: "Translation",
  component: Layout,
  redirect: "/translation/index",
  meta: {
    icon: "ri:translate-2",
    title: "Translation Management",
    rank: 5
  },
  children: [
    {
      path: "/translation/index",
      name: "TranslationIndex",
      component: () => import("@/views/translation/index.vue"),
      meta: {
        title: "Translation Management",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
