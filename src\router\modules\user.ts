import { $t } from "@/plugins/i18n";

const Layout = () => import("@/layout/index.vue");

export default {
  path: "/user",
  name: "User",
  component: Layout,
  redirect: "/user/index",
  meta: {
    icon: "ep:user",
    title: $t("User Management"),
    rank: 3
  },
  children: [
    {
      path: "/user/index",
      name: "UserManagement",
      component: () => import("@/views/user/index.vue"),
      meta: {
        title: $t("User Management"),
        showLink: true,
        auths: ["user.read"]
      }
    },
    {
      path: "/user/profile",
      name: "MyProfile",
      component: () => import("@/views/user/profile.vue"),
      meta: {
        title: $t("My Profile"),
        showLink: false,
        auths: ["user.read"]
      }
    },
    {
      path: "/user/profile/:id",
      name: "UserProfile",
      component: () => import("@/views/user/profile.vue"),
      meta: {
        title: $t("User Profile"),
        showLink: false,
        auths: ["user.read"]
      }
    }
  ]
} satisfies RouteConfigsTable;
