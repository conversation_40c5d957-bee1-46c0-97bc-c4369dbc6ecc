import { defineStore } from "pinia";
import { store } from "@/store";

import { useConvertKeyToCamel } from "@/utils/helpers";
import { getLanguages, getTranslations } from "@/views/language/utils/api";
import { storageLocal } from "@pureadmin/utils";

export interface Language {
  code: string;
  nativeName: string;
}

interface LanguageState {
  locale: string | null;
  languages: Language[] | null;
  messages: Record<string, Record<string, string>>;
  lastChecked: Record<string, number>;
}

export const useLanguageStore = defineStore("proCMS-language", {
  state: (): LanguageState => {
    const locale = storageLocal().getItem<string>("locale");
    console.log("--------------------------->", locale);
    const languages = storageLocal().getItem<Language[]>("languages");
    return {
      locale,
      languages,
      messages: {},
      lastChecked: {}
    };
  },
  getters: {
    getLanguages: state => state.languages
  },
  actions: {
    setLanguages(data: any) {
      this.languages = data;
    },
    setMessages(locale: string, data: any) {
      this.messages[locale] = data;
    },
    setLocale(locale: string) {
      this.locale = locale;
    },
    async fetchPublicLanguages() {
      try {
        const { data } = await getLanguages();
        this.setLanguages(useConvertKeyToCamel(data));
      } catch (error) {
        console.error("Failed to fetch languages:", error);
      }
    },
    async loadTranslations(locale: string) {
      try {
        const { data } = await getTranslations(locale);
        this.setMessages(locale, data);
      } catch (e) {
        console.error("Failed to load translations:", e);
      }
    }
  }
});

export function useLanguageStoreHook() {
  return useLanguageStore(store);
}
