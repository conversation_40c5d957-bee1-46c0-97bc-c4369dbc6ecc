import { defineStore } from "pinia";
import {
  type userType,
  store,
  router,
  resetRouter,
  routerArrays,
  storageLocal
} from "../utils";

import { useMultiTagsStoreHook } from "./multiTags";
import { setToken, removeToken, userKey, getToken } from "@/utils/auth";

import {
  type AuthLoginData,
  loginUser,
  logoutUser,
  registerUser,
  verifyEmail,
  resetPassword,
  resendVerification,
  socialLogin,
  type UserResult, forgotPassword
} from "@/views/auth/api/auth-api";
import { refreshTokenApi, type RefreshTokenResult } from "@/api/user";
import { useConvertKeyToCamel } from "@/utils/helpers";

export const useUserStore = defineStore("pure-user", {
  state: () => {
    const user = storageLocal().getItem<userType>(userKey);
    return {
      user: user || {},
      verifyCode: ""
    };
  },
  getters: {
    getVerifyCode: state => state.verifyCode,
    getUser: state => state.user,
    isRemembered: state => state.user?.isRemembered ?? false,
    loginDay: state => state.user?.loginDay ?? 1,
    userInfo: state => state.user,
    avatar: state => state.user?.avatar ?? "",
    username: state => state.user?.username ?? "",
    nickname: state => state.user?.nickname ?? "",
    roles: state => state.user?.roles ?? [],
    permissions: state => state.user?.permissions ?? [],
    accessToken: () => {
      const token = getToken();
      return token?.accessToken ?? "";
    }
  },
  actions: {
    setVerifyCode(code: string) {
      this.verifyCode = code;
    },

    loginByCredential(data: any) {
      return new Promise<any>((resolve, reject) => {
        loginUser(data)
          .then((result: UserResult<AuthLoginData>) => {
            if (result?.success) setToken(useConvertKeyToCamel(result.data));
            resolve(result);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    logOut() {
      return new Promise<void>(resolve => {
        console.log("🔓 Starting logout process...");

        // Clear local data first to prevent loops
        this.user = {};
        removeToken();
        useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
        resetRouter();

        console.log("🔓 Local data cleared, calling logout API...");
        logoutUser()
          .then(() => {
            console.log("✅ Logout API successful");
            router.push("/login").catch();
            resolve();
          })
          .catch(error => {
            // Even if logout API fails, still redirect to login
            console.warn(
              "⚠️ Logout API failed, but local data cleared:",
              error
            );
            router.push("/login").catch();
            resolve(); // Resolve instead of reject to prevent further issues
          });
      });
    },

    // Set user info
    setUserInfo(userInfo: any) {
      this.user = { ...this.user, ...userInfo };
    },

    // Clear user info
    clearUserInfo() {
      this.user = {};
    },

    // Update avatar
    updateAvatar(avatarUrl: string) {
      this.user = { ...this.user, avatar: avatarUrl };
      storageLocal().setItem(userKey, this.user);
    },

    // Forgot password
    forgotPassword(data: any) {
      return new Promise<UserResult>((resolve, reject) => {
        forgotPassword(data)
          .then(result => {
            resolve(result);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // Register user
    registerUser(data: any) {
      return new Promise<UserResult>((resolve, reject) => {
        registerUser(data)
          .then(result => {
            resolve(result);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // Verify email
    verifyEmail(data: any) {
      return new Promise<UserResult>((resolve, reject) => {
        verifyEmail(data)
          .then(result => {
            resolve(result);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // Reset password
    resetPassword(data: any) {
      return new Promise<UserResult>((resolve, reject) => {
        resetPassword(data)
          .then(result => {
            resolve(result);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // Resend verification
    resendVerification(data: any) {
      return new Promise<UserResult>((resolve, reject) => {
        resendVerification(data)
          .then(result => {
            resolve(result);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // Social login
    socialLogin(provider: string, data: any) {
      return new Promise<UserResult>((resolve, reject) => {
        socialLogin(provider, data)
          .then(result => {
            if (result?.success) setToken(result.data);
            resolve(result);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // Handle refresh token
    async handRefreshToken(data: any) {
      return new Promise<RefreshTokenResult>((resolve, reject) => {
        refreshTokenApi(data)
          .then(result => {
            if (result) {
              setToken(result.data);
              resolve(result);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // Check if user has specific role
    hasRole(role: string): boolean {
      return this.roles.includes(role);
    },

    // Check if user has any of the specified roles
    hasAnyRole(roles: string[]): boolean {
      return roles.some(role => this.roles.includes(role));
    },

    // Check if user has all specified roles
    hasAllRoles(roles: string[]): boolean {
      return roles.every(role => this.roles.includes(role));
    },

    // Check if user has specific permission
    hasPermission(permission: string): boolean {
      return (
        this.permissions.includes(permission) ||
        this.permissions.includes("*:*:*")
      );
    },

    // Check if user has any of the specified permissions
    hasAnyPermission(permissions: string[]): boolean {
      if (this.permissions.includes("*:*:*")) return true;
      return permissions.some(permission =>
        this.permissions.includes(permission)
      );
    },

    // Check if user has all specified permissions
    hasAllPermissions(permissions: string[]): boolean {
      if (this.permissions.includes("*:*:*")) return true;
      return permissions.every(permission =>
        this.permissions.includes(permission)
      );
    },



    // Update user profile
    updateProfile(profile: Partial<userType>) {
      this.user = { ...this.user, ...profile };
      // Update localStorage
      const userData = storageLocal().getItem<userType>(userKey);
      if (userData) {
        storageLocal().setItem(userKey, { ...userData, ...profile });
      }
    }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}
