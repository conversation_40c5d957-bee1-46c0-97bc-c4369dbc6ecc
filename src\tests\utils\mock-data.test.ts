import { describe, it, expect } from "vitest";
import {
  mockUsers,
  mockBots,
  mockChats,
  mockMessages,
  mockDashboardStats,
  paginate,
  delay
} from "@/utils/mock-data";

describe("Mock Data", () => {
  describe("mockUsers", () => {
    it("should have valid user structure", () => {
      expect(mockUsers).toHaveLength(3);
      
      const user = mockUsers[0];
      expect(user).toHaveProperty("id");
      expect(user).toHaveProperty("uuid");
      expect(user).toHaveProperty("name");
      expect(user).toHaveProperty("email");
      expect(user).toHaveProperty("avatar");
      expect(user).toHaveProperty("status");
      expect(user).toHaveProperty("createdAt");
      expect(user).toHaveProperty("updatedAt");
    });

    it("should have valid email format", () => {
      mockUsers.forEach(user => {
        expect(user.email).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
      });
    });
  });

  describe("mockBots", () => {
    it("should have valid bot structure", () => {
      expect(mockBots).toHaveLength(3);
      
      const bot = mockBots[0];
      expect(bot).toHaveProperty("id");
      expect(bot).toHaveProperty("uuid");
      expect(bot).toHaveProperty("name");
      expect(bot).toHaveProperty("description");
      expect(bot).toHaveProperty("aiModelId");
      expect(bot).toHaveProperty("systemPrompt");
      expect(bot).toHaveProperty("toolCallingMode");
      expect(bot).toHaveProperty("status");
    });

    it("should have valid tool calling modes", () => {
      const validModes = ["auto", "none", "required"];
      mockBots.forEach(bot => {
        expect(validModes).toContain(bot.toolCallingMode);
      });
    });

    it("should have valid statuses", () => {
      const validStatuses = ["draft", "review", "active", "paused", "banned"];
      mockBots.forEach(bot => {
        expect(validStatuses).toContain(bot.status);
      });
    });
  });

  describe("mockChats", () => {
    it("should have valid chat structure", () => {
      expect(mockChats).toHaveLength(3);
      
      const chat = mockChats[0];
      expect(chat).toHaveProperty("id");
      expect(chat).toHaveProperty("uuid");
      expect(chat).toHaveProperty("title");
      expect(chat).toHaveProperty("userId");
      expect(chat).toHaveProperty("botId");
      expect(chat).toHaveProperty("user");
      expect(chat).toHaveProperty("bot");
      expect(chat).toHaveProperty("messageCount");
      expect(chat).toHaveProperty("lastMessage");
    });

    it("should have valid relationships", () => {
      mockChats.forEach(chat => {
        expect(chat.user).toBeDefined();
        expect(chat.bot).toBeDefined();
        expect(chat.user.id).toBe(chat.userId);
        expect(chat.bot.id).toBe(chat.botId);
      });
    });
  });

  describe("mockMessages", () => {
    it("should have valid message structure", () => {
      expect(mockMessages.length).toBeGreaterThan(0);
      
      const message = mockMessages[0];
      expect(message).toHaveProperty("id");
      expect(message).toHaveProperty("uuid");
      expect(message).toHaveProperty("chatId");
      expect(message).toHaveProperty("role");
      expect(message).toHaveProperty("content");
      expect(message).toHaveProperty("contentType");
    });

    it("should have valid roles", () => {
      const validRoles = ["user", "assistant", "system"];
      mockMessages.forEach(message => {
        expect(validRoles).toContain(message.role);
      });
    });
  });

  describe("mockDashboardStats", () => {
    it("should have valid dashboard structure", () => {
      expect(mockDashboardStats).toHaveProperty("totalBots");
      expect(mockDashboardStats).toHaveProperty("totalChats");
      expect(mockDashboardStats).toHaveProperty("totalMessages");
      expect(mockDashboardStats).toHaveProperty("totalUsers");
      expect(mockDashboardStats).toHaveProperty("trends");
    });

    it("should have numeric values", () => {
      expect(typeof mockDashboardStats.totalBots).toBe("number");
      expect(typeof mockDashboardStats.totalChats).toBe("number");
      expect(typeof mockDashboardStats.totalMessages).toBe("number");
      expect(typeof mockDashboardStats.totalUsers).toBe("number");
    });

    it("should have valid trends", () => {
      const { trends } = mockDashboardStats;
      expect(trends).toHaveProperty("bots");
      expect(trends).toHaveProperty("chats");
      expect(trends).toHaveProperty("messages");
      expect(trends).toHaveProperty("users");

      Object.values(trends).forEach(trend => {
        expect(trend).toHaveProperty("value");
        expect(trend).toHaveProperty("isUp");
        expect(typeof trend.value).toBe("number");
        expect(typeof trend.isUp).toBe("boolean");
      });
    });
  });
});

describe("Utility Functions", () => {
  describe("paginate", () => {
    const testData = Array.from({ length: 25 }, (_, i) => ({ id: i + 1 }));

    it("should paginate data correctly", () => {
      const result = paginate(testData, 1, 10);
      
      expect(result.data).toHaveLength(10);
      expect(result.total).toBe(25);
      expect(result.currentPage).toBe(1);
      expect(result.perPage).toBe(10);
      expect(result.lastPage).toBe(3);
      expect(result.from).toBe(1);
      expect(result.to).toBe(10);
    });

    it("should handle last page correctly", () => {
      const result = paginate(testData, 3, 10);
      
      expect(result.data).toHaveLength(5);
      expect(result.currentPage).toBe(3);
      expect(result.from).toBe(21);
      expect(result.to).toBe(25);
    });

    it("should handle empty data", () => {
      const result = paginate([], 1, 10);
      
      expect(result.data).toHaveLength(0);
      expect(result.total).toBe(0);
      expect(result.lastPage).toBe(0);
    });

    it("should handle page beyond data", () => {
      const result = paginate(testData, 10, 10);
      
      expect(result.data).toHaveLength(0);
      expect(result.currentPage).toBe(10);
    });
  });

  describe("delay", () => {
    it("should delay execution", async () => {
      const start = Date.now();
      await delay(100);
      const end = Date.now();
      
      expect(end - start).toBeGreaterThanOrEqual(90); // Allow some tolerance
    });

    it("should use default delay", async () => {
      const start = Date.now();
      await delay();
      const end = Date.now();
      
      expect(end - start).toBeGreaterThanOrEqual(490); // Default 500ms with tolerance
    });
  });
});
