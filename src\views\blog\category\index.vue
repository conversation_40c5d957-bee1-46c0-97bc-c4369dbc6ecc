<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useCategoryHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";

const CategoryDrawerForm = defineAsyncComponent(
  () => import("./components/CategoryDrawerForm.vue")
);

const CategoryFilterForm = defineAsyncComponent(
  () => import("./components/CategoryFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  filterVisible,
  drawerVisible,
  drawerValues,
  categoryFormRef,
  fnGetCategories,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  handleDelete,
  handleBulkDelete,
  handleDestroy,
  handleBulkDestroy,
  handleRestore,
  handleBulkRestore,
  handleSubmit,
  handleFilter,
  handleEdit
} = useCategoryHook();

onMounted(() => {
  fnGetCategories();
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        border
        :title="$t('Category Management')"
        :columns="columns"
        @refresh="fnGetCategories"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-button
            type="text"
            class="font-bold text-[16px]"
            :disabled="!hasAuth('category.create')"
            @click="() => { drawerVisible = true; }"
          >
            <IconifyIconOnline icon="ep:plus" />
            {{ $t("Create") }}
          </el-button>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown trigger="click">
                <el-button type="primary" size="small" text>
                  {{ $t("More") }}
                  <IconifyIconOnline icon="ep:arrow-down" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <template v-if="filterRef.isTrashed === 'no'">
                      <el-dropdown-item
                        :disabled="!hasAuth('category.update')"
                        @click="handleEdit(row)"
                      >
                        <IconifyIconOnline
                          icon="ep:edit"
                          class="text-primary"
                        />
                        <span class="ml-2">
                          {{ $t("Edit") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!hasAuth('category.delete')"
                        @click="handleDelete(row.id)"
                      >
                        <IconifyIconOnline
                          icon="ep:delete"
                          class="text-red-600"
                        />
                        <span class="ml-2">
                          {{ $t("Move to Trash") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                    <template v-else>
                      <el-dropdown-item
                        :disabled="!hasAuth('category.restore')"
                        @click="handleRestore(row.id)"
                      >
                        <IconifyIconOnline
                          icon="ep:refresh-left"
                          class="text-green-600"
                        />
                        <span class="ml-2">
                          {{ $t("Restore") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!hasAuth('category.force-delete')"
                        @click="handleDestroy(row.id)"
                      >
                        <IconifyIconOnline
                          icon="tabler:trash-x"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Delete") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <CategoryDrawerForm
      ref="categoryFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        () => {
          categoryFormRef.value?.resetForm();
          drawerValues = { status: 'draft', publishedAt: null };
        }
      "
    />

    <CategoryFilterForm
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="() => { filterRef = { isTrashed: 'no' }; fnGetCategories(); }"
    />
  </div>
</template>
