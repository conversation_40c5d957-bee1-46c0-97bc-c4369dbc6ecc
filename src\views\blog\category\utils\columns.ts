import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import { h } from "vue";

// Helper functions
const formatDateTime = (date: string | null): string => {
  return date ? dayjs(date).format("YYYY-MM-DD HH:mm") : "-";
};

export const columns: TableColumnList = [
  {
    type: "selection",
    width: 30,
    sortable: false,
    fixed: "left"
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No."),
    fixed: "left"
  },
  {
    prop: "name",
    align: "left",
    sortable: true,
    minWidth: 200,
    headerRenderer: () => $t("Name"),
    showOverflowTooltip: false
  },
  {
    prop: "parent.name",
    align: "left",
    minWidth: 150,
    headerRenderer: () => $t("Parent Category"),
    showOverflowTooltip: false,
    cellRenderer: ({ row }) => row.parent?.name || "-"
  },
  {
    prop: "postsCount",
    align: "center",
    width: 100,
    headerRenderer: () => $t("Posts"),
    sortable: true,
    cellRenderer: ({ row }) => row.postsCount ?? 0
  },
  {
    prop: "status",
    align: "center",
    width: 100,
    sortable: true,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        draft: "warning",
        active: "success",
        inactive: "danger"
      };
      return h(
        ElTag,
        {
          type: statusColors[row.status],
          size: "small"
        },
        () => $t(row.status.toUpperCase())
      );
    }
  },
  {
    prop: "createdAt",
    width: 160,
    headerRenderer: () => $t("Created at"),
    formatter: (row: Record<string, any>) => formatDateTime(row.createdAt),
    sortable: true
  },
  {
    label: "",
    fixed: "right",
    width: 160,
    slot: "operation",
    sortable: false,
    align: "center"
  }
];
