import { reactive, ref, onMounted } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import {
  getCategories,
  deleteCategory,
  bulkDeleteCategories,
  destroyCategory,
  bulkDestroyCategories,
  restoreCategory,
  bulkRestoreCategories,
  createCategory,
  updateCategoryById
} from "@/views/blog/category/utils/auth-api";
import type { CategoryFilterProps } from "@/views/blog/category/utils/type";

export function useCategoryHook() {
  /*
   ***************************
   *   Data/State Management
   ***************************
   */
  const loading = ref(false);
  const filterRef = ref<CategoryFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({
    status: "draft",
    publishedAt: null
  });
  const categoryFormRef = ref();

  /*
   ***************************
   *   API Data Fetching
   ***************************
   */
  const fnGetCategories = async () => {
    loading.value = true;
    try {
      const response = await getCategories({
        ...filterRef.value,
        order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
        page: pagination.currentPage,
        limit: pagination.pageSize
      });
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;
    } catch (e) {
      console.error("Get Categories error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   API CRUD Operations
   ***************************
   */
  const fnHandleCreateCategory = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createCategory(useConvertKeyToSnake(formData));
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetCategories();
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateCategory = async (id: number, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateCategoryById(id, useConvertKeyToSnake(formData));
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetCategories();
        return true;
      }
      message(response?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };



  /*
   ***************************
   *   Table Event Handlers
   ***************************
   */
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetCategories();
  };

  const fnHandlePageChange = (page: number) => {
    pagination.currentPage = page;
    fnGetCategories();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetCategories();
  };

  /*
   ***************************
   *   Delete/Destroy/Restore API Handlers
   ***************************
   */
  const fnHandleDelete = async (id: number) => {
    try {
      const response = await deleteCategory(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetCategories();
        return true;
      }
      message(response?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    }
  };

  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      const response = await bulkDeleteCategories({ ids });
      if (response.success) {
        message(response.message || $t("Bulk delete successful"), {
          type: "success"
        });
        await fnGetCategories();
        return true;
      }
      message(response?.message || $t("Bulk delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Bulk delete failed"),
        {
          type: "error"
        }
      );
      return false;
    }
  };


  /*
   ***************************
   *   UI Action Handlers
   ***************************
   */
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to move this item to trash?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(id);
    } catch (error) {
      if (error !== "cancel") {
        console.error("Delete error:", error);
      }
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to move selected items to trash?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      await fnHandleBulkDelete(ids);
    } catch (error) {
      if (error !== "cancel") {
        console.error("Bulk delete error:", error);
      }
    }
  };

  const handleDestroy = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const response = await destroyCategory(id);
      if (response.success) {
        message(response.message || $t("Destroy successful"), {
          type: "success"
        });
        await fnGetCategories();
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Destroy failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  const handleBulkDestroy = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to destroy"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDestroyCategories({ ids });
      if (response.success) {
        message(response.message || $t("Bulk destroy successful"), {
          type: "success"
        });
        await fnGetCategories();
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Bulk destroy failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  const handleRestore = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this item?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );
      const response = await restoreCategory(id);
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetCategories();
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Restore failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  const handleBulkRestore = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected items?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkRestoreCategories({ ids });
      if (response.success) {
        message(response.message || $t("Bulk restore successful"), {
          type: "success"
        });
        await fnGetCategories();
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Bulk restore failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  /*
   ***************************
   *   Form Handlers
   ***************************
   */
  const handleSubmit = async (values: FieldValues) => {
    try {
      if (values.id) {
        const success = await fnHandleUpdateCategory(Number(values.id), values);
        if (success) {
          drawerVisible.value = false;
        }
      } else {
        const success = await fnHandleCreateCategory(values);
        if (success) {
          drawerVisible.value = false;
        }
      }
    } catch (error) {
      console.error("Submit error:", error);
    }
  };

  const handleFilter = (values: CategoryFilterProps) => {
    filterRef.value = values;
    pagination.currentPage = 1;
    fnGetCategories();
  };

  const handleEdit = (row: any) => {
    drawerValues.value = useConvertKeyToCamel(row);
    drawerVisible.value = true;
  };

  /*
   ***************************
   *   Lifecycle
   ***************************
   */
  onMounted(() => {
    fnGetCategories();
  });

  /*
   ***************************
   *   Return Hook Interface
   ***************************
   */
  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    filterVisible,
    drawerVisible,
    drawerValues,
    categoryFormRef,
    fnGetCategories,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,
    handleSubmit,
    handleFilter,
    handleEdit
  };
}
