export type FormItemProps = {
  id?: number | null;
  name?: string;
  slug?: string;
  description?: string;
  parentId?: number | null;
  sortOrder?: number;
  status?: "draft" | "published" | "archived";
  publishedAt?: string;
  // Translation fields
  locale?: string;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string;
};

export type CategoryFilterProps = {
  name?: string;
  slug?: string;
  parentId?: number | null;
  status?: "draft" | "published" | "archived" | "";
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
