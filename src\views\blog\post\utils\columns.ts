import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import { h } from "vue";

// Helper functions
const formatDateTime = (date: string | null): string => {
  return date ? dayjs(date).format("YYYY-MM-DD HH:mm") : "-";
};

export const columns: TableColumnList = [
  {
    type: "selection",
    width: 30,
    sortable: false,
    fixed: "left"
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No."),
    fixed: "left"
  },
  {
    prop: "title",
    align: "left",
    sortable: true,
    minWidth: 200,
    headerRenderer: () => $t("Title"),
    showOverflowTooltip: false
  },
  {
    prop: "category.name",
    align: "left",
    minWidth: 150,
    headerRenderer: () => $t("Category"),
    showOverflowTooltip: false,
    cellRenderer: ({ row }) => row.category?.name || "-"
  },
  {
    prop: "featured",
    align: "center",
    width: 100,
    sortable: true,
    headerRenderer: () => $t("Featured"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.featured ? "success" : "info",
          size: "small"
        },
        () => $t(row.featured ? "YES" : "NO")
      );
    }
  },
  {
    prop: "status",
    align: "center",
    width: 100,
    sortable: true,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        draft: "warning",
        published: "success",
        archived: "info",
        disabled: "danger"
      };
      return h(
        ElTag,
        {
          type: statusColors[row.status],
          size: "small"
        },
        () => $t(row.status.toUpperCase())
      );
    }
  },
  {
    prop: "createdAt",
    width: 160,
    headerRenderer: () => $t("Created at"),
    formatter: (row: Record<string, any>) => formatDateTime(row.createdAt),
    sortable: true
  },
  {
    prop: "publishedAt",
    width: 160,
    headerRenderer: () => $t("Published at"),
    formatter: (row: Record<string, any>) => formatDateTime(row.publishedAt),
    sortable: true
  },
  {
    label: "",
    fixed: "right",
    width: 160,
    slot: "operation",
    sortable: false,
    align: "center"
  }
];
