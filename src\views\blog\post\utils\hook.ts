import { reactive, ref, onMounted } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import {
  getPosts,
  deletePost,
  bulkDeletePosts,
  destroyPost,
  bulkDestroyPosts,
  restorePost,
  bulkRestorePosts,
  createPost,
  updatePostById
} from "@/views/blog/post/utils/auth-api";
import type { PostFilterProps } from "@/views/blog/post/utils/type";

export function usePostHook() {
  const loading = ref(false);
  const filterRef = ref<PostFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "created_at", sortOrder: "desc" });

  // Form refs
  const postFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref({});

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetPosts = async () => {
    try {
      loading.value = true;
      const params = useConvertKeyToSnake({
        ...filterRef.value,
        order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
        page: pagination.currentPage,
        limit: pagination.pageSize
      });
      const res = await getPosts(params);
      records.value = useConvertKeyToCamel(res.data.records);
      pagination.total = res.data.total;
    } catch (error) {
      console.error("Error fetching posts:", error);
      message($t("Failed to fetch posts"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * API CRUD Operations
   *************************** */

  const handleSubmit = async (values: FieldValues) => {
    try {
      loading.value = true;
      if (values.id) {
        await updatePostById(values.id, values);
        message($t("Updated successfully"), { type: "success" });
      } else {
        await createPost(values);
        message($t("Created successfully"), { type: "success" });
      }
      drawerVisible.value = false;
      fnGetPosts();
    } catch (error) {
      console.error("Error submitting post:", error);
      message($t("Operation failed"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Table Event Handlers
   *************************** */

  const fnHandleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const fnHandlePageChange = (val: number) => {
    pagination.currentPage = val;
    fnGetPosts();
  };

  const fnHandleSizeChange = (val: number) => {
    pagination.pageSize = val;
    fnGetPosts();
  };

  const fnHandleSortChange = ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetPosts();
  };
  /* ***************************
   * Delete handlers and actions
   *************************** */

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deletePost(row.id);
      message($t("Deleted successfully"), { type: "success" });
      fnGetPosts();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error deleting post:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeletePosts({ ids: selectedIds });
      message($t("Deleted successfully"), { type: "success" });
      fnGetPosts();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk deleting posts:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  /* ***************************
   * Destroy handlers and actions
   *************************** */

  const handleBulkDestroy = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to destroy"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently destroy selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDestroyPosts({ ids: selectedIds });
      message($t("Destroyed successfully"), { type: "success" });
      fnGetPosts();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk destroying posts:", error);
        message($t("Destroy failed"), { type: "error" });
      }
    }
  };

  /* ***************************
   * Restore handlers and actions
   *************************** */

  const handleBulkRestore = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      await bulkRestorePosts({ ids: selectedIds });
      message($t("Restored successfully"), { type: "success" });
      fnGetPosts();
    } catch (error) {
      console.error("Error bulk restoring posts:", error);
      message($t("Restore failed"), { type: "error" });
    }
  };
  /* ***************************
   * UI Action Handlers
   *************************** */

  const handleEdit = (row: any) => {
    drawerValues.value = { ...row };
    drawerVisible.value = true;
  };

  /* ***************************
   * Form Handlers
   *************************** */

  const handleFilter = (values: PostFilterProps) => {
    filterRef.value = values;
    pagination.currentPage = 1;
    fnGetPosts();
  };

  /* ***************************
   * Lifecycle
   *************************** */

  onMounted(() => {
    fnGetPosts();
  });

  /* ***************************
   * Return Hook Interface
   *************************** */

  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    handleBulkDelete,
    handleDelete,
    handleBulkDestroy,
    handleBulkRestore,
    fnGetPosts,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    filterVisible,
    drawerVisible,
    drawerValues,
    postFormRef,
    handleSubmit,
    handleFilter,
    handleEdit
  };
}



