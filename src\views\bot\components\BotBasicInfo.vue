<script setup lang="ts">
import { $t } from "@/plugins/i18n";

interface Props {
  values: {
    name?: string;
    model?: string;
    language?: string;
    description?: string;
  };
  aiModels: Array<{ label: string; value: string }>;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "update:values", values: any): void;
}>();

const updateField = (field: string, value: any) => {
  emit("update:values", { ...props.values, [field]: value });
};
</script>

<template>
  <div class="card">
    <h2 class="section-title">{{ $t("Basic Information") }}</h2>
    <el-row :gutter="20">
      <el-col :xs="24" :sm="8">
        <el-form-item :label="$t('AI Assistant Name')" prop="name">
          <el-input
            :model-value="props.values.name"
            clearable
            :placeholder="$t('Example: Administrative Procedure Consultant')"
            @update:model-value="val => updateField('name', val)"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="8">
        <el-form-item :label="$t('AI Model')" prop="model">
          <el-select
            :model-value="props.values.model"
            class="w-full"
            @update:model-value="val => updateField('model', val)"
          >
            <el-option
              v-for="model in props.aiModels"
              :key="model.value"
              :label="model.label"
              :value="model.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="8">
        <el-form-item :label="$t('Language')">
          <el-select
            :model-value="props.values.language"
            class="w-full"
            @update:model-value="val => updateField('language', val)"
          >
            <el-option :label="$t('Vietnamese')" value="vi" />
            <el-option :label="$t('English')" value="en" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item :label="$t('Description')">
      <el-input
        :model-value="props.values.description"
        type="textarea"
        :placeholder="$t('Brief description of AI Assistant functions.')"
        @update:model-value="val => updateField('description', val)"
      />
    </el-form-item>
  </div>
</template>

<style lang="scss" scoped>
.card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}
</style>
