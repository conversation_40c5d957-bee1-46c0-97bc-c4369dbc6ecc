<script setup lang="ts">
import { reactive, ref } from "vue";
import { $t } from "@/plugins/i18n";
import {
  UploadFilled,
  Document,
  Delete,
  Refresh
} from "@element-plus/icons-vue";
import { getToken } from "@/utils/auth";
import {
  getKnowledgeBaseFiles,
  getRemoveFile
} from "@/views/bot/utils/auth-api";
import { ElMessageBox, ElMessage } from "element-plus";

const props = defineProps<{
  drawerValues: any;
  loading: boolean;
  fileLibrary: any[];
  selectedFiles: string[];
}>();

const emit = defineEmits<{
  "update:file-library": [value: any[]];
  "update:selected-files": [value: string[]];
  "update:knowledge-text": [value: string];
  "update:new-uploads": [value: any[]];
  "update:library-files": [value: string[]];
  "update:bot-files": [value: any[]];
  "delete-file": [fileId: string];
  "refresh-library": [];
}>();

const knowledgeTab = ref("files");
const knowledgeFiles = ref([]);
const fileFilter = reactive({
  name: "",
  dateFrom: null,
  dateTo: null
});
const selectedLibraryFiles = ref([]);

const loadFileLibrary = async () => {
  try {
    const response = await getKnowledgeBaseFiles({
      name: fileFilter.name,
      dateFrom: fileFilter.dateFrom,
      dateTo: fileFilter.dateTo
    });
    emit("update:file-library", response.data);
  } catch (e) {
    console.error("Get Knowledge base files error:", e);
  }
};

const clearFilters = () => {
  fileFilter.name = "";
  fileFilter.dateFrom = null;
  fileFilter.dateTo = null;
  loadFileLibrary();
};

const handleFileChange = () => {
  // Don't emit files directly as they are UploadFiles, not the correct format
  // knowledge.newUploads should only contain {name, storage_path} objects
  // This will be handled in handleUploadSuccess when server responds
};

const handleUploadSuccess = (response: any, file: any) => {
  // Add uploaded file info to newUploads (tên file, storage_path)
  const currentNewUploads = props.drawerValues.knowledge?.newUploads || [];
  const newUpload = {
    name: response.data.name || file.name,
    storage_path: response.data.storage_path,
    size: file.size,
    type: file.type
  };
  const updatedNewUploads = [...currentNewUploads, newUpload];
  emit("update:new-uploads", updatedNewUploads);

  // Refresh library to show new uploaded file
  emit("refresh-library");

  ElMessage.success($t("File uploaded successfully"));
};

const handleLibrarySelectionChange = (selection: any) => {
  selectedLibraryFiles.value = selection;
};

const handleRemoveCurrentFile = async (file: any) => {
  try {
    await ElMessageBox.confirm(
      $t("Are you sure to remove this file from bot?")
    );

    // Remove from bot files (current files attached to bot)
    const currentBotFiles = props.drawerValues.knowledge?.botFiles || [];
    const updatedBotFiles = currentBotFiles.filter(
      (item: any) => item.id !== file.id
    );

    // Emit event to update botFiles
    emit("update:bot-files", updatedBotFiles);

    ElMessage.success($t("File removed from bot successfully"));
  } catch (e) {
    // User cancelled or error occurred
    console.log("Remove cancelled or error:", e);
  }
};

const beforeRemove = async (file: any) => {
  try {
    await ElMessageBox.confirm(
      $t("Are you sure you want to remove this file?"),
      $t("Warning"),
      {
        confirmButtonText: $t("OK"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    );

    // If file was successfully uploaded, remove it from server
    if (file.status === "success" && file.response?.data) {
      try {
        // Use id if available, otherwise use uuid, otherwise use storage_path
        const fileIdentifier =
          file.response.data.id ||
          file.response.data.uuid ||
          file.response.data.storage_path;
        await getRemoveFile({ id: fileIdentifier });

        // Also remove from newUploads if it exists there
        const currentNewUploads =
          props.drawerValues.knowledge?.newUploads || [];
        const updatedNewUploads = currentNewUploads.filter(
          (upload: any) =>
            upload.storage_path !== file.response.data.storage_path
        );
        emit("update:new-uploads", updatedNewUploads);

        ElMessage.success($t("File removed successfully"));
      } catch (error) {
        console.error("Error removing file from server:", error);
        ElMessage.error($t("Failed to remove file from server"));
        return false;
      }
    }

    return true;
  } catch {
    return false;
  }
};

const handleAddLibraryFiles = () => {
  // Get current library files (UUIDs)
  const currentLibraryFiles = props.drawerValues.knowledge?.libraryFiles || [];

  // Extract UUIDs from selected files and filter out duplicates
  const newLibraryUuids = selectedLibraryFiles.value
    .map((file: any) => file.uuid)
    .filter((uuid: string) => !currentLibraryFiles.includes(uuid));

  if (newLibraryUuids.length === 0) {
    ElMessage.warning($t("Selected files are already added to this bot"));
    return;
  }

  const updatedLibraryFiles = [...currentLibraryFiles, ...newLibraryUuids];
  emit("update:library-files", updatedLibraryFiles);

  // Clear selection
  selectedLibraryFiles.value = [];

  ElMessage.success(
    $t("Added {count} files to bot knowledge", {
      count: newLibraryUuids.length
    })
  );
};

const handleRemoveLibraryFiles = () => {
  selectedLibraryFiles.value = [];
};
</script>
<template>
  <div class="card">
    <h2 class="section-title">{{ $t("Knowledge Base") }}</h2>
    <el-tabs v-model="knowledgeTab" type="border-card">
      <el-tab-pane :label="$t('Upload New Files')" name="files">
        <el-upload
          ref="uploadRef"
          v-model:file-list="knowledgeFiles"
          class="w-full"
          drag
          action="/api/auth/knowledge-bases/files/upload"
          multiple
          :auto-upload="true"
          :headers="{
            Authorization: `Bearer ${getToken().accessToken ?? getToken()}`,
            'X-Requested-With': 'XMLHttpRequest'
          }"
          :before-remove="beforeRemove"
          @change="handleFileChange"
          @success="handleUploadSuccess"
        >
          <el-icon class="el-icon--upload">
            <upload-filled />
          </el-icon>
          <div class="el-upload__text">
            {{ $t("Drag files here or") }}
            <em>{{ $t("click to upload") }}</em>
          </div>
        </el-upload>
      </el-tab-pane>

      <el-tab-pane :label="$t('Text')" name="text">
        <el-input
          :model-value="props.drawerValues.knowledge.text"
          type="textarea"
          :rows="10"
          :placeholder="$t('Paste text content here.')"
          @update:model-value="val => emit('update:knowledge-text', val)"
        />
      </el-tab-pane>

      <el-tab-pane :label="$t('Choose from Library')" name="library">
        <div class="flex justify-between items-center mb-4">
          <p class="text-sm text-gray-500">
            {{
              $t(
                "Select previously uploaded documents to add to the knowledge for this Agent."
              )
            }}
          </p>
          <el-button
            :icon="Refresh"
            :loading="props.loading"
            size="small"
            @click="loadFileLibrary"
          >
            {{ $t("Refresh") }}
          </el-button>
        </div>

        <!-- Filter Controls -->
        <div class="mb-4 p-3 bg-gray-50 rounded-lg">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ $t("File Name") }}
              </label>
              <el-input
                v-model="fileFilter.name"
                :placeholder="$t('Search by file name...')"
                clearable
                size="small"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ $t("From Date") }}
              </label>
              <el-date-picker
                v-model="fileFilter.dateFrom"
                type="date"
                :placeholder="$t('Select start date')"
                size="small"
                style="width: 100%"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ $t("To Date") }}
              </label>
              <el-date-picker
                v-model="fileFilter.dateTo"
                type="date"
                :placeholder="$t('Select end date')"
                size="small"
                style="width: 100%"
              />
            </div>
          </div>
          <div class="flex justify-between items-center mt-3">
            <span class="text-xs text-gray-500">
              {{
                $t("Total files: {count}", {
                  count: props.fileLibrary.length
                })
              }}
            </span>
            <el-button size="small" @click="clearFilters">
              {{ $t("Clear Filters") }}
            </el-button>
          </div>
        </div>

        <div
          v-if="!props.loading && props.fileLibrary.length === 0"
          class="text-center py-8 text-gray-500"
        >
          <el-icon class="text-4xl mb-2">
            <Document />
          </el-icon>
          <p>{{ $t("No files found in the library.") }}</p>
          <p class="text-xs mt-2">
            {{ $t("Upload files to the knowledge base first.") }}
          </p>
        </div>

        <el-table
          v-else
          v-loading="props.loading"
          :data="props.fileLibrary"
          height="250"
          @selection-change="handleLibrarySelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" :label="$t('File Name')" />
          <el-table-column prop="type" :label="$t('Type')" width="100" />
          <el-table-column prop="size" :label="$t('Size')" width="120" />
          <el-table-column prop="date" :label="$t('Upload Date')" width="180" />
        </el-table>

        <!-- Action buttons for library files -->
        <div v-if="selectedLibraryFiles.length > 0" class="mt-4 flex gap-2">
          <el-button type="primary" size="small" @click="handleAddLibraryFiles">
            {{ $t("Add Selected Files") }} ({{ selectedLibraryFiles.length }})
          </el-button>
          <el-button size="small" @click="handleRemoveLibraryFiles">
            {{ $t("Clear Selection") }}
          </el-button>
        </div>
      </el-tab-pane>
      <!-- Current Files Tab - Only show when editing -->
      <el-tab-pane
        v-if="props.drawerValues.uuid"
        :label="$t('Current Files')"
        name="current"
      >
        <p class="text-sm text-gray-500 mb-4">
          {{
            $t(
              "Files currently attached to this bot. You can remove files that are no longer needed."
            )
          }}
        </p>

        <div
          v-if="!props.drawerValues.knowledge?.botFiles?.length"
          class="text-center py-8 text-gray-500"
        >
          <el-icon class="text-4xl mb-2">
            <Document />
          </el-icon>
          <p>{{ $t("No files attached to this bot yet.") }}</p>
        </div>

        <el-table
          v-else
          :data="props.drawerValues.knowledge?.botFiles || []"
          height="250"
        >
          <el-table-column prop="name" :label="$t('File Name')" />
          <el-table-column prop="type" :label="$t('Type')" width="100" />
          <el-table-column prop="size" :label="$t('Size')" width="120" />
          <el-table-column
            prop="createdAt"
            :label="$t('Upload Date')"
            width="180"
          />
          <el-table-column :label="$t('Actions')" width="100" align="center">
            <template #default="{ row }">
              <el-popconfirm
                :title="$t('Are you sure you want to remove this file?')"
                @confirm="handleRemoveCurrentFile(row)"
              >
                <template #reference>
                  <el-button type="danger" size="small" :icon="Delete" circle />
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
