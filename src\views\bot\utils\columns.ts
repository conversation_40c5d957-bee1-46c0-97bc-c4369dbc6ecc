import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElAvatar } from "element-plus";
import { h } from "vue";
import { IconifyIconOnline } from "@/components/ReIcon";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    prop: "logo",
    align: "center",
    width: 90,
    headerRenderer: () => "",
    cellRenderer: ({ row }) => {
      return h(ElAvatar, {
        size: 50,
        src: row.logoUrl,
        alt: row.name
      });
    }
  },
  {
    prop: "name",
    align: "left",
    sortable: false,
    minWidth: 210,
    headerRenderer: () => $t("Bot Name"),
    cellRenderer: ({ row }) => {
      return h("div", { class: "flex flex-col" }, [
        h(
          "div",
          {
            class: "font-medium text-gray-900 text-sm line-clamp-2"
          },
          row.name || "Untitled Bot"
        ),
        h(
          "div",
          {
            class: "text-sm text-gray-500 mt-1"
          },
          row.description
        )
      ]);
    }
  },
  {
    prop: "aiModel",
    align: "left",
    width: 180,
    headerRenderer: () => $t("Model AI"),
    cellRenderer: ({ row }) => {
      return h(
        "span",
        {
          class: "text-sm font-medium text-gray-900"
        },
        row.aiModel?.name || "-"
      );
    }
  },
  {
    prop: "status",
    align: "left",
    width: 130,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        draft: {
          type: "info",
          text: "Draft",
          class: "bg-gray-100 text-gray-800",
          icon: "ri:draft-line",
          iconClass: "text-gray-600"
        },
        review: {
          type: "warning",
          text: "Review",
          class: "bg-yellow-100 text-yellow-800",
          icon: "ri:time-line",
          iconClass: "text-yellow-600"
        },
        active: {
          type: "success",
          text: "Production",
          class: "bg-green-100 text-green-800",
          icon: "ri:checkbox-circle-fill",
          iconClass: "text-green-600"
        },
        paused: {
          type: "warning",
          text: "Development",
          class: "bg-orange-100 text-orange-800",
          icon: "ri:time-fill",
          iconClass: "text-orange-600"
        },
        banned: {
          type: "danger",
          text: "Inactive",
          class: "bg-red-100 text-red-800",
          icon: "ri:close-circle-fill",
          iconClass: "text-red-600"
        }
      };

      const config = statusColors[row.status] || statusColors.draft;

      return h(
        "span",
        {
          class: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.class}`
        },
        [
          h(IconifyIconOnline, {
            icon: config.icon,
            class: `w-3 h-3 mr-1.5 ${config.iconClass}`
          }),
          config.text
        ]
      );
    }
  },
  {
    prop: "createdAt",
    align: "left",
    width: 140,
    headerRenderer: () => $t("Created At"),
    cellRenderer: ({ row }) => {
      return h(
        "span",
        {
          class: "text-sm text-gray-600"
        },
        dayjs(row.createdAt).format("YYYY-MM-DD HH:mm")
      );
    }
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
