export type FormItemProps = {
  id?: number | null;
  uuid?: string;
  name?: string;
  logo?: string;
  description?: string;
  ownerId?: number;
  ownerType?: string;
  owner?: any;
  aiModelId?: number;
  aiModel?: any;
  systemPrompt?: string;
  greetingMessage?: string;
  starterMessages?: any;
  closingMessage?: string;
  parameters?: any;
  toolCallingMode?: "auto" | "none" | "required";
  visibility?: string;
  botType?: string;
  status?: "draft" | "review" | "active" | "paused" | "banned";
  metadata?: any;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
};

export type BotFilterProps = {
  name?: string;
  status?: "draft" | "review" | "active" | "paused" | "banned";
  visibility?: string;
  botType?: string;
  ownerId?: number;
  ownerType?: string;
  aiModelId?: number;
  toolCallingMode?: "auto" | "none" | "required";
  isTrashed?: "yes" | "no";
  [key: string]: any;
};

// Safe Bot type for list display (without sensitive fields)
export type BotListItem = {
  uuid?: string;
  name?: string;
  logo?: string;
  description?: string;
  greetingMessage?: string;
  starterMessages?: any;
  closingMessage?: string;
  toolCallingMode?: "auto" | "none" | "required";
  visibility?: string;
  botType?: string;
  status?: "draft" | "review" | "active" | "paused" | "banned";
  createdAt?: string;
  updatedAt?: string;
  // Safe nested objects without IDs
  aiModel?: {
    name?: string;
    provider?: string;
  };
  owner?: {
    name?: string;
    avatar?: string;
  };
};
