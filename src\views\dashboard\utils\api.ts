import { http } from "@/utils/http";
import type { Result } from "@/utils/response";

export const getDashboardStats = () => {
  return http.request<Result>("get", "/api/auth/dashboard/stats");
};

export const getBotStats = () => {
  return http.request<Result>("get", "/api/auth/dashboard/bot-stats");
};

export const getChatStats = () => {
  return http.request<Result>("get", "/api/auth/dashboard/chat-stats");
};

export const getMessageStats = () => {
  return http.request<Result>("get", "/api/auth/dashboard/message-stats");
};

export const getUserStats = () => {
  return http.request<Result>("get", "/api/auth/dashboard/user-stats");
};

export const getRecentActivities = (params?: object) => {
  return http.request<Result>("get", "/api/auth/dashboard/recent-activities", {
    params
  });
};

export const getTopBots = (params?: object) => {
  return http.request<Result>("get", "/api/auth/dashboard/top-bots", {
    params
  });
};

export const getTopUsers = (params?: object) => {
  return http.request<Result>("get", "/api/auth/dashboard/top-users", {
    params
  });
};
