<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);

const formRef = ref();

onMounted(() => {});

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Name")),
    prop: "name",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input knowledge base name"),
        trigger: ["blur"]
      },
      {
        min: 2,
        max: 255,
        message: $t("Length must be between 2 and 255 characters"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 15 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    required: true,
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" },
      { label: $t("Processing"), value: "processing" },
      { label: $t("Failed"), value: "failed" }
    ],
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    colProps: { span: 9 }
  },
  {
    label: computed(() => $t("Type")),
    prop: "type",
    valueType: "select",
    required: true,
    options: [
      { label: $t("File"), value: "file" },
      { label: $t("Text"), value: "text" },
      { label: $t("URL"), value: "url" },
      { label: $t("Document"), value: "document" }
    ],
    fieldProps: {
      placeholder: ""
    }
  },

  {
    label: computed(() => $t("Attachment Path")),
    prop: "storagePath",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter attachment file path")
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Content")),
    prop: "content",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      rows: 6
    }
  },
  {
    label: computed(() => $t("Metadata")),
    prop: "metadata",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      rows: 4
    }
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="40%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
