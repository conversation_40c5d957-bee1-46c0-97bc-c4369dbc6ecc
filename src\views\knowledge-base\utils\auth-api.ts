import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/knowledge-base/utils/type";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getKnowledgeBases = (params?: object) => {
  return http.request<Result>("get", "/api/auth/knowledge-bases", {
    params
  });
};

export const getKnowledgeBaseById = (id: number) => {
  return http.request<Result>("get", `/api/auth/knowledge-bases/${id}`);
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createKnowledgeBase = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/knowledge-bases", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateKnowledgeBaseById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/knowledge-bases/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteKnowledgeBaseById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/knowledge-bases/${id}`);
};

export const bulkDeleteKnowledgeBases = (data: { ids: number[] }) => {
  return http.request<Result>(
    "delete",
    "/api/auth/knowledge-bases/bulk-delete",
    {
      data
    }
  );
};

/*
 ***************************
 *   Hard Delete Operations
 ***************************
 */
export const destroyKnowledgeBaseById = (id: number) => {
  return http.request<Result>(
    "delete",
    `/api/auth/knowledge-bases/${id}/force`
  );
};

export const bulkDestroyKnowledgeBases = (data: { ids: number[] }) => {
  return http.request<Result>(
    "delete",
    "/api/auth/knowledge-bases/bulk-destroy",
    {
      data
    }
  );
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreKnowledgeBaseById = (id: number) => {
  return http.request<Result>(
    "post",
    `/api/auth/knowledge-bases/${id}/restore`
  );
};

export const bulkRestoreKnowledgeBases = (data: { ids: number[] }) => {
  return http.request<Result>(
    "post",
    "/api/auth/knowledge-bases/bulk-restore",
    {
      data
    }
  );
};

/*
 ***************************
 *   Dropdown Operations
 ***************************
 */
export const dropdownKnowledgeBases = () => {
  return http.request<r>("get", "/api/auth/knowledge-bases/dropdown");
};
