import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag, ElButton, ElTooltip } from "element-plus";
import { h } from "vue";
import { capitalized } from "@/utils/helpers";
import { IconifyIconOnline } from "@/components/ReIcon";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "name",
    align: "left",
    sortable: true,
    minWidth: 200,
    headerRenderer: () => $t("Knowledge Base Name")
  },
  {
    prop: "type",
    align: "center",
    sortable: true,
    width: 120,
    headerRenderer: () => $t("Type"),
    cellRenderer: ({ row }) => {
      const typeColors = {
        file: "primary",
        text: "success", 
        url: "warning",
        document: "info"
      };
      return h(
        ElTag,
        {
          type: typeColors[row.type] || "info",
          size: "small"
        },
        () => capitalized(row.type || "")
      );
    }
  },

  {
    prop: "status",
    align: "center",
    sortable: true,
    width: 120,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        active: "success",
        inactive: "info",
        processing: "warning",
        failed: "danger"
      };
      return h(
        ElTag,
        {
          type: statusColors[row.status] || "info",
          size: "small"
        },
        () => $t(capitalized(row.status)).toUpperCase()
      );
    }
  },
  {
    prop: "storagePath",
    align: "center",
    sortable: false,
    width: 120,
    headerRenderer: () => $t("Attachment"),
    cellRenderer: ({ row }) => {
      if (!row.storagePath || row.type === "text") return "-";

      // Lấy đuôi file để xác định icon
      const getFileExtension = (path: string) => {
        return path.split('.').pop()?.toLowerCase() || '';
      };

      // Xác định icon dựa trên đuôi file
      const getFileIcon = (extension: string) => {
        const iconMap = {
          // Documents
          'pdf': 'mdi:file-pdf',
          'doc': 'mdi:file-word',
          'docx': 'mdi:file-word',
          'xls': 'mdi:file-excel',
          'xlsx': 'mdi:file-excel',
          'ppt': 'mdi:file-powerpoint',
          'pptx': 'mdi:file-powerpoint',
          'txt': 'mdi:file-document',

          // Images
          'jpg': 'mdi:file-image',
          'jpeg': 'mdi:file-image',
          'png': 'mdi:file-image',
          'gif': 'mdi:file-image',
          'svg': 'mdi:file-image',

          // Archives
          'zip': 'mdi:file-zip',
          'rar': 'mdi:file-zip',
          '7z': 'mdi:file-zip',

          // Others
          'json': 'mdi:file-code',
          'xml': 'mdi:file-code',
          'csv': 'mdi:file-delimited'
        };
        return iconMap[extension] || 'mdi:file';
      };

      const extension = getFileExtension(row.storagePath);
      const icon = getFileIcon(extension);

      return h(
        ElTooltip,
        {
          content: row.storagePath,
          placement: "top"
        },
        {
          default: () => h(
            ElButton,
            {
              size: "small",
              type: "primary",
              link: true,
              onClick: () => {
                // Tải file xuống
                if (row.storagePath) {
                  const link = document.createElement('a');
                  link.href = row.storagePath;
                  link.download = row.name || 'document';
                  link.target = '_blank';
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }
              }
            },
            {
              default: () => [
                h(IconifyIconOnline, {
                  icon: icon,
                  style: { marginRight: '4px' }
                }),
                extension.toUpperCase() || 'FILE'
              ]
            }
          )
        }
      );
    }
  },
  {
    prop: "createdAt",
    width: 160,
    sortable: true,
    headerRenderer: () => $t("Created at"),
    formatter: ({ createdAt }) =>
      createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm") : "-"
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
