import { reactive, ref } from "vue";
import { useConvertKeyToCamel, useConvert<PERSON>eyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import {
  getKnowledgeBases,
  deleteKnowledgeBaseById,
  bulkDeleteKnowledgeBases,
  destroyKnowledgeBaseById,
  bulkDestroyKnowledgeBases,
  restoreKnowledgeBaseById,
  bulkRestoreKnowledgeBases,
  createKnowledgeBase,
  updateKnowledgeBaseById
} from "@/views/knowledge-base/utils/auth-api";
import type { KnowledgeBaseFilterProps } from "@/views/knowledge-base/utils/type";

export function useKnowledgeBaseHook() {
  /*
   ***************************
   *   Data/State Management
   ***************************
   */
  const loading = ref(false);
  const filterRef = ref<KnowledgeBaseFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({
    status: "active",
    type: "text"
  });
  const knowledgeBaseFormRef = ref();

  /*
   ***************************
   *   API Data Fetching
   ***************************
   */
  const fnGetKnowledgeBases = async () => {
    loading.value = true;
    try {
      const response = await getKnowledgeBases(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;

      console.log("Knowledge bases loaded:", records.value);
    } catch (e) {
      console.error("Get Knowledge bases error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Table Event Handlers
   ***************************
   */
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = async ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    await fnGetKnowledgeBases();
  };

  const fnHandlePageChange = async (val: number) => {
    pagination.currentPage = val;
    await fnGetKnowledgeBases();
  };

  const fnHandleSizeChange = async (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    await fnGetKnowledgeBases();
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(id);
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleDelete = async (id: number) => {
    try {
      loading.value = true;
      const response = await deleteKnowledgeBaseById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const handleBulkDelete = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkDelete(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkDeleteKnowledgeBases({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk delete Knowledge bases error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Destroy handlers and actions
   ***************************
   */
  const handleDestroy = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDestroy(id);
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleDestroy = async (id: number) => {
    try {
      loading.value = true;
      const response = await destroyKnowledgeBaseById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const handleBulkDestroy = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkDestroy(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleBulkDestroy = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkDestroyKnowledgeBases({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk destroy Knowledge bases error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Restore handlers and actions
   ***************************
   */
  const handleRestore = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleRestore(id);
    } catch {
      console.log("Restore cancelled");
    }
  };
  const fnHandleRestore = async (id: number) => {
    try {
      loading.value = true;
      const response = await restoreKnowledgeBaseById(id);
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Restore failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const handleBulkRestore = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to restore"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkRestore(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Restore cancelled");
    }
  };
  const fnHandleBulkRestore = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkRestoreKnowledgeBases({ ids });
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk restore Knowledge bases error:", error);
      message(error.response?.data?.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Form handlers and actions
   ***************************
   */
  const handleSubmit = async (values: FieldValues) => {
    drawerVisible.value = false;
    if (values.id != null) {
      await fnHandleUpdateKnowledgeBase(Number(values.id), values);
      return;
    }
    const success = await fnHandleCreateKnowledgeBase(values);
    if (success) {
      drawerValues.value = {
        status: "active",
        type: "text"
      };
      knowledgeBaseFormRef.value?.resetForm();
    }
  };
  const handleFilter = async (values: KnowledgeBaseFilterProps) => {
    filterRef.value = values;
    filterVisible.value = false;
    await fnGetKnowledgeBases();
  };

  /*
   ***************************
   *   API handlers and actions
   ***************************
   */
  const fnHandleCreateKnowledgeBase = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createKnowledgeBase(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const fnHandleUpdateKnowledgeBase = async (id: number, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateKnowledgeBaseById(id, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    knowledgeBaseFormRef,

    // API Handlers
    fnGetKnowledgeBases,
    fnHandleCreateKnowledgeBase,
    fnHandleUpdateKnowledgeBase,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
