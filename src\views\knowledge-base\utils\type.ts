export type FormItemProps = {
  id?: number | null;
  uuid?: string;
  ownerType?: string;
  ownerId?: number;
  ownerName?: string;
  name?: string;
  type?: "file" | "text" | "url" | "document";
  storagePath?: string;
  content?: string;
  status?: "active" | "inactive" | "processing" | "failed";
  metadata?: string;
  // Relations
  user?: {
    id: number;
    name: string;
  };
  organization?: {
    id: number;
    name: string;
  };
};

export type KnowledgeBaseFilterProps = {
  name?: string;
  type?: "file" | "text" | "url" | "document" | "";
  status?: "active" | "inactive" | "processing" | "failed" | "";
  ownerType?: string;
  ownerName?: string;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
