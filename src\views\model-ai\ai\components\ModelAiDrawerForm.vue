<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, h, onMounted, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { dropdownModelCategories } from "@/views/model-ai/categories/utils/auth-api";
import { dropdownProviders } from "@/views/model-ai/provider/utils/auth-api";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);
const formRef = ref();
const providers = ref([]);
const categories = ref([]);
const categoriesIds = ref([]);

onMounted(() => {});

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Model key")),
    prop: "key",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input model key"),
        trigger: ["blur"]
      },
      {
        pattern: /^[a-z0-9-_]+$/,
        message: $t(
          "Key can only contain lowercase letters, numbers, hyphens and underscores"
        ),
        trigger: "blur"
      }
    ],
    fieldProps: {
      placeholder: $t("e.g., gpt-4, claude-3-sonnet")
    },
    colProps: { span: 17 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" },
      { label: $t("Draft"), value: "draft" }
    ],
    colProps: { span: 7 }
  },
  {
    label: computed(() => $t("Model name")),
    prop: "name",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input model name"),
        trigger: ["blur", "change"]
      },
      {
        min: 2,
        max: 100,
        message: $t("Length should be between 2 and 100 characters"),
        trigger: ["blur", "change"]
      }
    ],
    fieldProps: {
      placeholder: $t("e.g., GPT-4, Claude 3 Sonnet")
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Model Provider")),
    prop: "modelProviderId",
    valueType: "select",
    required: true,
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    options: computed(() => [
      ...providers.value.map((provider: any) => ({
        label: provider.name,
        value: provider.id
      }))
    ]),
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Model Category")),
    prop: "categoriesIds",
    valueType: "select",
    required: true,
    fieldProps: {
      placeholder: "",
      clearable: false,
      multiple: true,
      collapseTags: true,
      collapseTagsTooltip: true,
      maxCollapseTags: 3
    },
    options: computed(() => [
      ...categories.value.map((category: any) => ({
        label: category.name,
        value: Number(category.id)
      }))
    ]),
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("API Endpoint")),
    prop: "apiEndpoint",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input API endpoint"),
        trigger: ["blur"]
      },
      {
        pattern: /^\/.*$/,
        message: $t("API endpoint must start with /"),
        trigger: "blur"
      }
    ],
    fieldProps: {
      placeholder: $t("e.g., /chat/completions, /messages")
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Streaming")),
    prop: "streaming",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ],
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Vision")),
    prop: "vision",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ],
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Function Calling (Tools)")),
    prop: "functionCalling",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ],
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Set Default")),
    prop: "isDefault",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    options: [
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ],
    colProps: { span: 6 }
  },
  {
    label: computed(() => $t("Sort Order")),
    prop: "sortOrder",
    valueType: "input-number",
    fieldProps: {
      min: 1,
      max: 999,
      placeholder: "1"
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Description")),
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Model description"),
      rows: 4
    },
    colProps: { span: 24 }
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

const loadProviders = () => {
  dropdownProviders()
    .then(({ data }) => {
      providers.value = useConvertKeyToCamel(data);
    })
    .catch(e => console.log("Error loading providers:------------->", e));
};

const loadCategories = async () => {
  await dropdownModelCategories()
    .then(({ data }) => {
      categories.value = useConvertKeyToCamel(data);
    })
    .catch(e => console.log("Error loading categories:----------->", e))
    .finally();
};

watch(
  () => props.visible,
  async () => {
    if (props.visible) {
      await loadCategories().then(() => {
        console.log(categoriesIds.value);
      });

      loadProviders();
    }
  }
);

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="50%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
