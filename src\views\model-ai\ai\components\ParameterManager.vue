<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { <PERSON><PERSON><PERSON>on, ElCard, ElTag, ElInputNumber, ElInput, ElSelect, ElOption, ElSwitch, ElTooltip, ElIcon } from "element-plus";
import { Plus, Delete, Setting } from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";

interface ParameterDefinition {
  key: string;
  type: 'number' | 'string' | 'boolean' | 'select';
  min?: number;
  max?: number;
  step?: number;
  options?: { label: string; value: any }[];
  description: string;
  defaultValue: any;
}

interface ParameterValue {
  key: string;
  value: any;
  type: string;
}

const props = defineProps<{
  defaultParameters?: Record<string, any>;
  allowedParameters?: Record<string, any>;
}>();

const emit = defineEmits<{
  (e: "update:defaultParameters", value: Record<string, any>): void;
  (e: "update:allowedParameters", value: Record<string, any>): void;
}>();

// <PERSON><PERSON><PERSON> nghĩa các parameter phổ biến của AI models
const availableParameters: ParameterDefinition[] = [
  {
    key: "temperature",
    type: "number",
    min: 0,
    max: 2,
    step: 0.1,
    description: "Controls randomness: 0 = deterministic, 2 = very creative",
    defaultValue: 0.7
  },
  {
    key: "max_tokens",
    type: "number", 
    min: 1,
    max: 8192,
    step: 1,
    description: "Maximum number of tokens to generate",
    defaultValue: 1000
  },
  {
    key: "top_p",
    type: "number",
    min: 0,
    max: 1,
    step: 0.01,
    description: "Nucleus sampling: only consider top P probability mass",
    defaultValue: 0.9
  },
  {
    key: "frequency_penalty",
    type: "number",
    min: -2,
    max: 2,
    step: 0.1,
    description: "Penalize repeated tokens based on frequency",
    defaultValue: 0
  },
  {
    key: "presence_penalty",
    type: "number",
    min: -2,
    max: 2,
    step: 0.1,
    description: "Penalize repeated tokens based on presence",
    defaultValue: 0
  },
  {
    key: "top_k",
    type: "number",
    min: 1,
    max: 100,
    step: 1,
    description: "Only consider top K most likely tokens",
    defaultValue: 50
  },
  {
    key: "repetition_penalty",
    type: "number",
    min: 0.1,
    max: 2,
    step: 0.1,
    description: "Penalty for repeating tokens",
    defaultValue: 1.1
  },
  {
    key: "stream",
    type: "boolean",
    description: "Enable streaming response",
    defaultValue: false
  },
  {
    key: "stop_sequences",
    type: "string",
    description: "Stop generation when these sequences are encountered (JSON array)",
    defaultValue: "[]"
  },
  {
    key: "seed",
    type: "number",
    min: 0,
    max: 999999999,
    step: 1,
    description: "Random seed for reproducible outputs",
    defaultValue: null
  }
];

// State
const selectedParameter = ref<string>("");
const defaultParams = ref<ParameterValue[]>([]);
const allowedParams = ref<ParameterValue[]>([]);

// Computed
const availableOptions = computed(() => {
  const usedKeys = [...defaultParams.value, ...allowedParams.value].map(p => p.key);
  return availableParameters.filter(param => !usedKeys.includes(param.key));
});

const selectedParamDef = computed(() => {
  return availableParameters.find(p => p.key === selectedParameter.value);
});

// Methods
const addParameter = (isDefault: boolean = true) => {
  if (!selectedParamDef.value) return;
  
  const param: ParameterValue = {
    key: selectedParamDef.value.key,
    value: selectedParamDef.value.defaultValue,
    type: selectedParamDef.value.type
  };
  
  if (isDefault) {
    defaultParams.value.push(param);
  } else {
    allowedParams.value.push(param);
  }
  
  selectedParameter.value = "";
  updateEmits();
};

const removeParameter = (index: number, isDefault: boolean = true) => {
  if (isDefault) {
    defaultParams.value.splice(index, 1);
  } else {
    allowedParams.value.splice(index, 1);
  }
  updateEmits();
};

const updateParameterValue = (index: number, value: any, isDefault: boolean = true) => {
  if (isDefault) {
    defaultParams.value[index].value = value;
  } else {
    allowedParams.value[index].value = value;
  }
  updateEmits();
};

const updateEmits = () => {
  const defaultObj = defaultParams.value.reduce((acc, param) => {
    acc[param.key] = param.value;
    return acc;
  }, {} as Record<string, any>);
  
  const allowedObj = allowedParams.value.reduce((acc, param) => {
    acc[param.key] = param.value;
    return acc;
  }, {} as Record<string, any>);
  
  emit("update:defaultParameters", defaultObj);
  emit("update:allowedParameters", allowedObj);
};

const getParameterDef = (key: string) => {
  return availableParameters.find(p => p.key === key);
};

// Initialize from props
watch(() => props.defaultParameters, (newVal) => {
  if (newVal) {
    defaultParams.value = Object.entries(newVal).map(([key, value]) => ({
      key,
      value,
      type: getParameterDef(key)?.type || 'string'
    }));
  }
}, { immediate: true });

watch(() => props.allowedParameters, (newVal) => {
  if (newVal) {
    allowedParams.value = Object.entries(newVal).map(([key, value]) => ({
      key,
      value,
      type: getParameterDef(key)?.type || 'string'
    }));
  }
}, { immediate: true });
</script>

<template>
  <div class="parameter-manager">
    <!-- Parameter Selector -->
    <div class="mb-4">
      <div class="flex items-center space-x-3 mb-3">
        <ElSelect
          v-model="selectedParameter"
          placeholder="Select a parameter to add"
          class="flex-1"
          clearable
        >
          <ElOption
            v-for="param in availableOptions"
            :key="param.key"
            :label="param.key"
            :value="param.key"
          >
            <div class="flex flex-col">
              <span class="font-medium">{{ param.key }}</span>
              <span class="text-xs text-gray-500">{{ param.description }}</span>
            </div>
          </ElOption>
        </ElSelect>
        
        <ElButton
          type="primary"
          :icon="Plus"
          :disabled="!selectedParameter"
          @click="addParameter(true)"
        >
          Add to Default
        </ElButton>
        
        <ElButton
          type="success"
          :icon="Plus"
          :disabled="!selectedParameter"
          @click="addParameter(false)"
        >
          Add to Allowed
        </ElButton>
      </div>
      
      <!-- Parameter Info -->
      <div v-if="selectedParamDef" class="bg-blue-50 p-3 rounded-lg">
        <div class="flex items-center space-x-2 mb-2">
          <ElTag :type="selectedParamDef.type === 'number' ? 'warning' : selectedParamDef.type === 'boolean' ? 'success' : 'info'">
            {{ selectedParamDef.type }}
          </ElTag>
          <span class="font-medium">{{ selectedParamDef.key }}</span>
        </div>
        <p class="text-sm text-gray-600 mb-2">{{ selectedParamDef.description }}</p>
        <div v-if="selectedParamDef.type === 'number'" class="text-xs text-gray-500">
          Range: {{ selectedParamDef.min }} - {{ selectedParamDef.max }}
          <span v-if="selectedParamDef.step">, Step: {{ selectedParamDef.step }}</span>
        </div>
      </div>
    </div>

    <!-- Default Parameters -->
    <ElCard class="mb-4">
      <template #header>
        <div class="flex items-center space-x-2">
          <ElIcon><Setting /></ElIcon>
          <span class="font-medium">{{ $t("Default Parameters") }}</span>
          <ElTag size="small">{{ defaultParams.length }}</ElTag>
        </div>
      </template>
      
      <div v-if="defaultParams.length === 0" class="text-center text-gray-500 py-4">
        No default parameters configured
      </div>
      
      <div v-else class="space-y-3">
        <div
          v-for="(param, index) in defaultParams"
          :key="`default-${param.key}`"
          class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-1">
              <span class="font-medium">{{ param.key }}</span>
              <ElTag size="small" :type="param.type === 'number' ? 'warning' : param.type === 'boolean' ? 'success' : 'info'">
                {{ param.type }}
              </ElTag>
            </div>
            
            <!-- Number Input -->
            <ElInputNumber
              v-if="param.type === 'number'"
              :model-value="param.value"
              :min="getParameterDef(param.key)?.min"
              :max="getParameterDef(param.key)?.max"
              :step="getParameterDef(param.key)?.step || 0.1"
              :precision="getParameterDef(param.key)?.step === 1 ? 0 : 2"
              size="small"
              @update:model-value="(val) => updateParameterValue(index, val, true)"
            />
            
            <!-- Boolean Switch -->
            <ElSwitch
              v-else-if="param.type === 'boolean'"
              :model-value="param.value"
              @update:model-value="(val) => updateParameterValue(index, val, true)"
            />
            
            <!-- String Input -->
            <ElInput
              v-else
              :model-value="param.value"
              size="small"
              @update:model-value="(val) => updateParameterValue(index, val, true)"
            />
          </div>
          
          <ElTooltip content="Remove parameter">
            <ElButton
              type="danger"
              :icon="Delete"
              size="small"
              circle
              @click="removeParameter(index, true)"
            />
          </ElTooltip>
        </div>
      </div>
    </ElCard>

    <!-- Allowed Parameters -->
    <ElCard>
      <template #header>
        <div class="flex items-center space-x-2">
          <ElIcon><Setting /></ElIcon>
          <span class="font-medium">{{ $t("Allowed Parameters") }}</span>
          <ElTag size="small">{{ allowedParams.length }}</ElTag>
        </div>
      </template>
      
      <div v-if="allowedParams.length === 0" class="text-center text-gray-500 py-4">
        No allowed parameters configured
      </div>
      
      <div v-else class="space-y-3">
        <div
          v-for="(param, index) in allowedParams"
          :key="`allowed-${param.key}`"
          class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-1">
              <span class="font-medium">{{ param.key }}</span>
              <ElTag size="small" :type="param.type === 'number' ? 'warning' : param.type === 'boolean' ? 'success' : 'info'">
                {{ param.type }}
              </ElTag>
            </div>
            
            <!-- Number Input -->
            <ElInputNumber
              v-if="param.type === 'number'"
              :model-value="param.value"
              :min="getParameterDef(param.key)?.min"
              :max="getParameterDef(param.key)?.max"
              :step="getParameterDef(param.key)?.step || 0.1"
              :precision="getParameterDef(param.key)?.step === 1 ? 0 : 2"
              size="small"
              @update:model-value="(val) => updateParameterValue(index, val, false)"
            />
            
            <!-- Boolean Switch -->
            <ElSwitch
              v-else-if="param.type === 'boolean'"
              :model-value="param.value"
              @update:model-value="(val) => updateParameterValue(index, val, false)"
            />
            
            <!-- String Input -->
            <ElInput
              v-else
              :model-value="param.value"
              size="small"
              @update:model-value="(val) => updateParameterValue(index, val, false)"
            />
          </div>
          
          <ElTooltip content="Remove parameter">
            <ElButton
              type="danger"
              :icon="Delete"
              size="small"
              circle
              @click="removeParameter(index, false)"
            />
          </ElTooltip>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
.parameter-manager {
  width: 100%;
}
</style>
