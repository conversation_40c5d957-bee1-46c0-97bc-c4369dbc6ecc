import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/model-ai/ai/utils/type";

export const getModelAis = (params?: object) => {
  return http.request<Result>("get", "/api/auth/model-ai", {
    params
  });
};

export const getModelAiById = (id: number) => {
  return http.request<Result>("get", `/api/auth/model-ai/${id}`);
};

export const createModelAi = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/model-ai", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateModelAiById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/model-ai/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const destroyModelAiById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/model-ai/${id}`);
};

export const bulkDestroyModelAis = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/model-ai/bulk-destroy", {
    data
  });
};

export const restoreModelAiById = (id: number) => {
  return http.request<Result>("put", `/api/auth/model-ai/${id}/restore`);
};

export const bulkRestoreModelAis = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/model-ai/bulk-restore", {
    data
  });
};

export const deleteModelAiById = (id: number) => {
  return http.request<Result>(
    "delete",
    `/api/auth/model-ai/${id}/force-delete`
  );
};

export const bulkDeleteModelAis = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/model-ai/bulk-delete", {
    data
  });
};

export const dropdownModelAis = () => {
  return http.request<Result>("get", "/api/auth/model-ai/dropdown");
};

export const updateOrCreateService = (data: FormItemProps) => {
  return http.request<Result>("post", `/api/auth/model-ai/service`, {
    data: useConvertKeyToSnake(data)
  });
};
