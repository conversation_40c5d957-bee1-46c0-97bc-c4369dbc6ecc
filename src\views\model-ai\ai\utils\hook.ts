import { reactive, ref } from "vue";
import {
  getModelA<PERSON>,
  createModelAi,
  updateModelAiById,
  deleteModelAiById,
  bulkDeleteModelAis,
  bulkDestroyModelAis,
  destroyModelAiById,
  restoreModelAiById,
  bulkRestoreModelAis,
  updateOrCreateService
} from "../utils/auth-api";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import type { ModelAiFilterProps } from "@/views/model-ai/ai/utils/type";
import { cloneDeep } from "@pureadmin/utils";

export function useModelAiHook() {
  // Data/State
  const loading = ref(false);
  const filterRef = ref<ModelAiFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const serviceVisible = ref(false);
  const drawerValues = ref<FieldValues>({
    streaming: true,
    vision: false,
    functionCalling: false,
    isDefault: false,
    status: "active",
    sortOrder: 1
  });

  const serviceValues = ref<FieldValues>({
    service: {
      status: "active",
      billingType: "per_request",
      costPer1kTokens: 0,
      costPer1kInput: 0,
      costPer1kOutput: 0,
      costPerRequest: 0
    }
  });

  const modelAiFormRef = ref();
  const serviceFormRef = ref();

  const fnGetModelAis = async () => {
    loading.value = true;
    try {
      const response = await getModelAis(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;
    } catch (e) {
      console.error("Get Model AIs error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  // Table Event Handlers
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = async ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    await fnGetModelAis();
  };

  const fnHandlePageChange = async (val: number) => {
    pagination.currentPage = val;
    await fnGetModelAis();
  };

  const fnHandleSizeChange = async (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    await fnGetModelAis();
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(id);
    } catch {
      console.log("Delete cancelled");
    }
  };

  const fnHandleDelete = async (id: number) => {
    try {
      loading.value = true;
      const response = await deleteModelAiById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetModelAis();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleBulkDelete = async () => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleBulkDelete(ids);
    } catch {
      console.log("Delete cancelled");
    }
  };

  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkDeleteModelAis({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetModelAis();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk delete Model AIs error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Destroy handlers and actions
   ***************************
   */
  const handleDestroy = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDestroy(id);
    } catch {
      console.log("Delete cancelled");
    }
  };

  const fnHandleDestroy = async (id: number) => {
    try {
      loading.value = true;
      const response = await destroyModelAiById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetModelAis();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleBulkDestroy = async () => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleBulkDestroy(ids);
    } catch {
      console.log("Delete cancelled");
    }
  };

  const fnHandleBulkDestroy = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkDestroyModelAis({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetModelAis();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk destroy Model AIs error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Restore handlers and actions
   ***************************
   */
  const handleRestore = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleRestore(id);
    } catch {
      console.log("Restore cancelled");
    }
  };

  const fnHandleRestore = async (id: number) => {
    try {
      loading.value = true;
      const response = await restoreModelAiById(id);
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetModelAis();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message ||
          error?.message ||
          $t("Restores failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleBulkRestore = async () => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to restore"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleBulkRestore(ids);
    } catch {
      console.log("Restore cancelled");
    }
  };

  const fnHandleBulkRestore = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkRestoreModelAis({ ids });
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetModelAis();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk restore Model AIs error:", error);
      message(error.response?.data?.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   CTA handlers
   ***************************
   */
  const handleSubmit = async (values: FieldValues) => {
    const formData = cloneDeep(values);
    formData.categories = values.categoriesIds;
    delete formData.categoriesIds;

    if (formData.id != null) {
      await fnHandleUpdateModelAi(Number(formData.id), formData);
      return;
    }
    const success = await fnHandleCreateModelAi(formData);
    if (success) {
      drawerValues.value = {
        streaming: true,
        vision: false,
        functionCalling: false,
        isDefault: false,
        status: "active",
        sortOrder: 1
      };
      modelAiFormRef.value?.resetForm();
    }
  };

  const handleFilter = async (values: ModelAiFilterProps) => {
    filterRef.value = values;
    await fnGetModelAis();
  };

  const fnHandleCreateModelAi = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createModelAi(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetModelAis();
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateModelAi = async (id: number, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateModelAiById(id, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetModelAis();
        return true;
      }
      message(response?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   AI Service handlers
   ***************************
   */
  const handleSubmitService = async (values: FieldValues) => {
    const formData = cloneDeep(values);
    const _service = formData.service;
    _service.model_ai_id = values.id;
    await fnHandleUpdateOrCreateService(_service);
  };

  const fnHandleUpdateOrCreateService = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateOrCreateService(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetModelAis();
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    serviceVisible,
    drawerValues,
    serviceValues,
    modelAiFormRef,
    serviceFormRef,

    // API Handlers
    fnGetModelAis,
    fnHandleCreateModelAi,
    fnHandleUpdateModelAi,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter,
    handleSubmitService
  };
}
