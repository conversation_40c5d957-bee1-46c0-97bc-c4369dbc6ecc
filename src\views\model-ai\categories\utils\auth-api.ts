import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/model-ai/categories/utils/type";

export const getModelCategories = (params?: object) => {
  return http.request<Result>("get", "/api/auth/model-categories", {
    params
  });
};

export const getModelCategoryById = (id: number) => {
  return http.request<Result>("get", `/api/auth/model-categories/${id}`);
};

export const createModelCategory = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/model-categories", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateModelCategoryById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/model-categories/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const destroyModelCategoryById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/model-categories/${id}`);
};

export const bulkDestroyModelCategories = (data: { ids: number[] }) => {
  return http.request<Result>(
    "delete",
    "/api/auth/model-categories/bulk-destroy",
    {
      data
    }
  );
};

export const restoreModelCategoryById = (id: number) => {
  return http.request<Result>(
    "put",
    `/api/auth/model-categories/${id}/restore`
  );
};

export const bulkRestoreModelCategories = (data: { ids: number[] }) => {
  return http.request<Result>(
    "put",
    "/api/auth/model-categories/bulk-restore",
    {
      data
    }
  );
};

export const deleteModelCategoryById = (id: number) => {
  return http.request<Result>(
    "delete",
    `/api/auth/model-categories/${id}/force-delete`
  );
};

export const bulkDeleteModelCategories = (data: { ids: number[] }) => {
  return http.request<Result>(
    "delete",
    "/api/auth/model-categories/bulk-delete",
    {
      data
    }
  );
};

export const dropdownModelCategories = () => {
  return http.request<Result>("get", "/api/auth/model-categories/dropdown");
};


export const createModelService = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/model-services", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateModelServiceById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/model-services/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};
