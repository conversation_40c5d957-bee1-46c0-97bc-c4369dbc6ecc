<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);

const formRef = ref();

onMounted(() => {});

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Provider code")),
    prop: "key",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input provider code"),
        trigger: ["blur", "change"]
      },
      {
        min: 2,
        max: 50,
        message: $t("Length should be between 2 and 50 characters"),
        trigger: ["blur", "change"]
      }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ],
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Provider nane")),
    prop: "name",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input provider nane"),
        trigger: ["blur", "change"]
      },
      {
        min: 2,
        max: 50,
        message: $t("Length should be between 2 and 50 characters"),
        trigger: ["blur", "change"]
      }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Provider url")),
    prop: "baseUrl",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input provider url"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Api Key")),
    prop: "apiKey",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input api key"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Description")),
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      showWordLimit: true,
      autosize: { minRows: 2, maxRows: 4 }
    }
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="50%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
