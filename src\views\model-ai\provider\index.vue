<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useProviderHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import TableButtons from "@/components/TableButtons.vue";
import TableOperations from "@/components/TableOperations.vue";
import PureTable from "@pureadmin/table";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { hasAuth } from "@/router/utils";

// Lazy load components
const ProviderDrawerForm = defineAsyncComponent(
  () => import("./components/ProviderDrawerForm.vue")
);

const ProviderFilterForm = defineAsyncComponent(
  () => import("./components/ProviderFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetProviders,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  providerFormRef,
  handleSubmit,
  handleFilter,
  handleBulkDestroy,
  handleBulkRestore,
  handleDestroy,
  handleRestore
} = useProviderHook();

onMounted(() => {
  nextTick(() => {
    fnGetProviders();
  });
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="!w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('Provider Management')"
        :columns="columns"
        @refresh="fnGetProviders"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('model-provider.create')"
              @click="
                () => {
                  drawerVisible = true;
                }
              "
            >
              <IconifyIconOnline
                :icon="
                  hasAuth('model-provider.create')
                    ? 'flat-color-icons:plus'
                    : 'icons8:plus'
                "
                width="18px"
              />
            </el-button>
          </el-tooltip>
          <template v-if="filterRef.isTrashed === 'yes'">
            <el-tooltip :content="$t('Restore')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length === 0 ||
                  (multipleSelection.length > 0 &&
                    !hasAuth('model-provider.restore'))
                "
                @click="handleBulkRestore"
              >
                <IconifyIconOnline
                  icon="tabler:restore"
                  width="18px"
                  :class="{ 'text-blue-600': multipleSelection.length > 0 }"
                />
              </el-button>
            </el-tooltip>
            <el-tooltip :content="$t('Bulk Delete')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length === 0 ||
                  (multipleSelection.length > 0 &&
                    !hasAuth('model-provider.force-delete'))
                "
                @click="handleBulkDelete"
              >
                <IconifyIconOnline
                  icon="tabler:trash-x-filled"
                  width="18px"
                  :class="{ 'text-red-700': multipleSelection.length > 0 }"
                />
              </el-button>
            </el-tooltip>
          </template>
          <template v-else>
            <el-tooltip :content="$t('Bulk Destroy')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length == 0 ||
                  (multipleSelection.length > 0 &&
                    !hasAuth('model-provider.destroy'))
                "
                @click="handleBulkDestroy"
              >
                <IconifyIconOnline
                  icon="tabler:trash"
                  width="18px"
                  :class="{ 'text-red-700': multipleSelection.length > 0 }"
                />
              </el-button>
            </el-tooltip>
          </template>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            border
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown split-button trigger="click" size="small">
                {{ $t("Action") }}
                <template #dropdown>
                  <el-dropdown-menu class="min-w-[130px]">
                    <el-dropdown-item disabled>
                      {{ $t("Action") }}
                    </el-dropdown-item>
                    <template v-if="filterRef.isTrashed == 'no'">
                      <el-dropdown-item
                        :disabled="!hasAuth('model-provider.update')"
                        divided
                        @click="
                          () => {
                            drawerValues = clone(row);
                            drawerVisible = true;
                          }
                        "
                      >
                        <IconifyIconOnline
                          icon="ant-design:edit-outlined"
                          class="text-blue-700"
                        />
                        <span class="ml-2">{{ $t("Edit") }}</span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!hasAuth('model-provider.destroy')"
                        @click="handleDestroy(row.id)"
                      >
                        <IconifyIconOnline
                          icon="tabler:trash"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Destroy") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                    <template v-else>
                      <el-dropdown-item
                        :disabled="!hasAuth('model-provider.restore')"
                        @click="handleRestore(row.id)"
                      >
                        <IconifyIconOnline
                          icon="tabler:restore"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Restore") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!hasAuth('model-provider.force-delete')"
                        @click="handleDelete(row.id)"
                      >
                        <IconifyIconOnline
                          icon="tabler:trash-x"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Delete") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>
    <ProviderDrawerForm
      ref="providerFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        () => {
          providerFormRef.value?.resetForm();
          drawerValues = { status: 'active', isTrashed: 'no' };
        }
      "
    />
    <ProviderFilterForm
      ref="filterFormRef"
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="
        () => {
          filterRef = { isTrashed: 'no' };
          fnGetProviders();
        }
      "
    />
  </div>
</template>

<style lang="scss" scoped>
.main {
  @apply p-4;
}
</style>
