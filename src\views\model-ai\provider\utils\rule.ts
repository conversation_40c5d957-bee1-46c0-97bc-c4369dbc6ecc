import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";

const providerRules = reactive<FormRules>({
  key: [
    {
      required: true,
      message: $t("Please enter the provider code"),
      trigger: "blur"
    },
    {
      min: 3,
      max: 50,
      message: $t("Length must be between 3 and 50 characters"),
      trigger: "blur"
    }
  ],
  name: [
    {
      required: true,
      message: $t("Please enter the provider name"),
      trigger: "blur"
    },
    {
      min: 3,
      max: 250,
      message: $t("Length must be between 3 and 250 characters"),
      trigger: "blur"
    }
  ]
});

export { providerRules };
