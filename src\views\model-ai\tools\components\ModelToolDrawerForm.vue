<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { dropdownModelCategories } from "@/views/model-ai/categories/utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);
const formRef = ref();
const categories = ref([]);

onMounted(async () => {
  try {
    const { data } = await dropdownModelCategories();
    categories.value = useConvertKeyToCamel(data);
  } catch (error) {
    console.error("Failed to load categories:", error);
  }
});

const toolTypes = [
  { label: $t("Function"), value: "function" },
  { label: $t("Plugin"), value: "plugin" },
  { label: $t("Integration"), value: "integration" },
  { label: $t("Custom"), value: "custom" }
];

const statusOptions = [
  { label: $t("Active"), value: "active" },
  { label: $t("Inactive"), value: "inactive" }
];

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Tool Name")),
    prop: "name",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input tool name"),
        trigger: ["blur", "change"]
      },
      {
        min: 2,
        max: 100,
        message: $t("Length should be between 2 and 100 characters"),
        trigger: ["blur", "change"]
      }
    ],
    fieldProps: {
      placeholder: $t("Enter tool name")
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Slug")),
    prop: "slug",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input tool slug"),
        trigger: ["blur"]
      },
      {
        pattern: /^[a-z0-9-_]+$/,
        message: $t(
          "Slug can only contain lowercase letters, numbers, hyphens and underscores"
        ),
        trigger: "blur"
      }
    ],
    fieldProps: {
      placeholder: $t("e.g., web-search, calculator")
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Tool Type")),
    prop: "toolType",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select tool type"),
        trigger: ["blur", "change"]
      }
    ],
    options: toolTypes,
    fieldProps: {
      placeholder: $t("Select tool type")
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Version")),
    prop: "version",
    valueType: "input",
    fieldProps: {
      placeholder: $t("e.g., 1.0.0, v2.1")
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Categories")),
    prop: "categories",
    valueType: "checkbox",
    options: computed(() =>
      categories.value.map(cat => ({
        label: cat.name,
        value: cat.id
      }))
    ),
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    required: true,
    options: statusOptions,
    fieldProps: {
      placeholder: $t("Select status")
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Is Active")),
    prop: "isActive",
    valueType: "switch",
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Parameters")),
    prop: "parameters",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("JSON parameters configuration (optional)"),
      rows: 4
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Configuration")),
    prop: "configuration",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("JSON configuration (optional)"),
      rows: 4
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Description")),
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Tool description"),
      showWordLimit: true,
      autosize: { minRows: 3, maxRows: 6 }
    },
    colProps: { span: 24 }
  }
];

const resetForm = () => {
  formRef.value?.resetFields();
};

const handleSubmit = async () => {
  try {
    loading.value = true;
    const isValid = await formRef.value?.validate();
    if (isValid) {
      emit("submit", props.values);
      emit("update:visible", false);
    }
  } catch (error) {
    console.error("Form validation failed:", error);
  } finally {
    loading.value = false;
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="60%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Tool Information") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style lang="scss" scoped>
.custom-group-header {
  @apply flex items-center gap-2;
}

.custom-footer {
  @apply flex justify-end gap-2;
}
</style>
