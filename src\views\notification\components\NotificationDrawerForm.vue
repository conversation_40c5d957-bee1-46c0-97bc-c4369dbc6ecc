<script setup lang="ts">
import { reactive, ref, computed, watch } from "vue";
import { $t } from "@/plugins/i18n";
import type { FormInstance, FormRules } from "element-plus";

interface Props {
  visible: boolean;
  data?: any;
}

interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "confirm", data: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  data: () => ({})
});

const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const formData = reactive({
  id: undefined,
  title: "",
  content: "",
  type: "info",
  priority: "medium",
  targetType: "all",
  targetIds: [],
  scheduledAt: "",
  expiresAt: "",
  isActive: true,
  status: "draft",
  metadata: {}
});

const isEdit = computed(() => !!formData.id);
const drawerTitle = computed(() =>
  isEdit.value ? $t("Edit Notification") : $t("Create Notification")
);

const typeOptions = [
  { label: $t("Info"), value: "info" },
  { label: $t("Success"), value: "success" },
  { label: $t("Warning"), value: "warning" },
  { label: $t("Error"), value: "error" },
  { label: $t("Announcement"), value: "announcement" }
];

const priorityOptions = [
  { label: $t("Low"), value: "low" },
  { label: $t("Medium"), value: "medium" },
  { label: $t("High"), value: "high" },
  { label: $t("Urgent"), value: "urgent" }
];

const targetTypeOptions = [
  { label: $t("All Users"), value: "all" },
  { label: $t("Role"), value: "role" },
  { label: $t("User"), value: "user" },
  { label: $t("Group"), value: "group" }
];

const statusOptions = [
  { label: $t("Draft"), value: "draft" },
  { label: $t("Scheduled"), value: "scheduled" }
];

const rules = reactive<FormRules>({
  title: [
    {
      required: true,
      message: $t("Please input notification title"),
      trigger: "blur"
    },
    {
      min: 2,
      max: 255,
      message: $t("Length should be between 2 and 255 characters"),
      trigger: "blur"
    }
  ],
  content: [
    {
      required: true,
      message: $t("Please input notification content"),
      trigger: "blur"
    }
  ],
  type: [
    {
      required: true,
      message: $t("Please select notification type"),
      trigger: "change"
    }
  ],
  priority: [
    {
      required: true,
      message: $t("Please select priority"),
      trigger: "change"
    }
  ],
  targetType: [
    {
      required: true,
      message: $t("Please select target type"),
      trigger: "change"
    }
  ]
});

const handleClose = () => {
  emit("update:visible", false);
  resetForm();
};

const handleConfirm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(valid => {
    if (valid) {
      const submitData = { ...formData };

      // Clean up data
      if (!submitData.scheduledAt) delete submitData.scheduledAt;
      if (!submitData.expiresAt) delete submitData.expiresAt;
      if (submitData.targetType === "all") {
        submitData.targetIds = [];
      }

      emit("confirm", submitData);
    }
  });
};

const resetForm = () => {
  formRef.value?.resetFields();
  Object.assign(formData, {
    id: undefined,
    title: "",
    content: "",
    type: "info",
    priority: "medium",
    targetType: "all",
    targetIds: [],
    scheduledAt: "",
    expiresAt: "",
    isActive: true,
    status: "draft",
    metadata: {}
  });
};

// Watch for data changes
watch(
  () => props.data,
  newData => {
    if (newData && Object.keys(newData).length > 0) {
      Object.assign(formData, {
        id: newData.id,
        title: newData.title || "",
        content: newData.content || "",
        type: newData.type || "info",
        priority: newData.priority || "medium",
        targetType: newData.targetType || "all",
        targetIds: newData.targetIds || [],
        scheduledAt: newData.scheduledAt || "",
        expiresAt: newData.expiresAt || "",
        isActive: newData.isActive !== undefined ? newData.isActive : true,
        status: newData.status || "draft",
        metadata: newData.metadata || {}
      });
    } else {
      resetForm();
    }
  },
  { immediate: true, deep: true }
);

// Watch for visible changes
watch(
  () => props.visible,
  visible => {
    if (!visible) {
      resetForm();
    }
  }
);
</script>

<template>
  <el-drawer
    :model-value="visible"
    :title="drawerTitle"
    direction="rtl"
    size="600px"
    @close="handleClose"
  >
    <template #default>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="140px"
        label-position="top"
        class="px-4"
      >
        <div class="grid grid-cols-1 gap-4">
          <el-form-item :label="$t('Notification Title')" prop="title">
            <el-input
              v-model="formData.title"
              :placeholder="$t('Enter notification title')"
              maxlength="255"
              show-word-limit
            />
          </el-form-item>

          <el-form-item :label="$t('Notification Content')" prop="content">
            <el-input
              v-model="formData.content"
              type="textarea"
              :placeholder="$t('Enter notification content')"
              :rows="4"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>

          <div class="grid grid-cols-2 gap-4">
            <el-form-item :label="$t('Type')" prop="type">
              <el-select
                v-model="formData.type"
                :placeholder="$t('Please select notification type')"
                class="w-full"
              >
                <el-option
                  v-for="option in typeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('Priority')" prop="priority">
              <el-select
                v-model="formData.priority"
                :placeholder="$t('Please select priority')"
                class="w-full"
              >
                <el-option
                  v-for="option in priorityOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <el-form-item :label="$t('Target Type')" prop="targetType">
              <el-select
                v-model="formData.targetType"
                :placeholder="$t('Please select target type')"
                class="w-full"
              >
                <el-option
                  v-for="option in targetTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('Status')" prop="status">
              <el-select
                v-model="formData.status"
                :placeholder="$t('Please select status')"
                class="w-full"
              >
                <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <el-form-item
            v-if="formData.targetType !== 'all'"
            :label="$t('Target Users')"
            prop="targetIds"
          >
            <el-select
              v-model="formData.targetIds"
              :placeholder="$t('Please select target users')"
              multiple
              filterable
              class="w-full"
            >
              <!-- This would be populated with actual users/roles/groups -->
              <el-option label="Sample User 1" value="1" />
              <el-option label="Sample User 2" value="2" />
            </el-select>
          </el-form-item>

          <div class="grid grid-cols-2 gap-4">
            <el-form-item :label="$t('Scheduled At')" prop="scheduledAt">
              <el-date-picker
                v-model="formData.scheduledAt"
                type="datetime"
                :placeholder="$t('Select schedule time')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="w-full"
              />
            </el-form-item>

            <el-form-item :label="$t('Expires At')" prop="expiresAt">
              <el-date-picker
                v-model="formData.expiresAt"
                type="datetime"
                :placeholder="$t('Select expiry time')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="w-full"
              />
            </el-form-item>
          </div>

          <el-form-item :label="$t('Active Status')" prop="isActive">
            <el-switch
              v-model="formData.isActive"
              :active-text="$t('Active')"
              :inactive-text="$t('Inactive')"
            />
          </el-form-item>
        </div>
      </el-form>
    </template>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <el-button @click="handleClose">
          {{ $t("Cancel") }}
        </el-button>
        <el-button type="primary" @click="handleConfirm">
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>
