<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import { $t } from "@/plugins/i18n";
import type { FormInstance } from "element-plus";

interface Props {
  visible: boolean;
  data?: any;
}

interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "confirm", data: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  data: () => ({})
});

const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const formData = reactive({
  title: "",
  type: "",
  priority: "",
  targetType: "",
  status: "",
  isRead: "",
  isActive: "",
  isTrashed: "no",
  dateRange: []
});

const typeOptions = [
  { label: $t("Info"), value: "info" },
  { label: $t("Success"), value: "success" },
  { label: $t("Warning"), value: "warning" },
  { label: $t("Error"), value: "error" },
  { label: $t("Announcement"), value: "announcement" }
];

const priorityOptions = [
  { label: $t("Low"), value: "low" },
  { label: $t("Medium"), value: "medium" },
  { label: $t("High"), value: "high" },
  { label: $t("Urgent"), value: "urgent" }
];

const targetTypeOptions = [
  { label: $t("All Users"), value: "all" },
  { label: $t("Role"), value: "role" },
  { label: $t("User"), value: "user" },
  { label: $t("Group"), value: "group" }
];

const statusOptions = [
  { label: $t("Draft"), value: "draft" },
  { label: $t("Scheduled"), value: "scheduled" },
  { label: $t("Sent"), value: "sent" },
  { label: $t("Expired"), value: "expired" },
  { label: $t("Cancelled"), value: "cancelled" }
];

const isReadOptions = [
  { label: $t("All"), value: "" },
  { label: $t("Read"), value: "true" },
  { label: $t("Unread"), value: "false" }
];

const isActiveOptions = [
  { label: $t("All"), value: "" },
  { label: $t("Active"), value: "true" },
  { label: $t("Inactive"), value: "false" }
];

const trashOptions = [
  { label: $t("Active"), value: "no" },
  { label: $t("Trashed"), value: "yes" }
];

const handleClose = () => {
  emit("update:visible", false);
};

const handleConfirm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(valid => {
    if (valid) {
      const submitData = { ...formData };

      // Convert date range
      if (submitData.dateRange && submitData.dateRange.length === 2) {
        submitData.startDate = submitData.dateRange[0];
        submitData.endDate = submitData.dateRange[1];
        delete submitData.dateRange;
      }

      // Convert boolean strings
      if (submitData.isRead === "true") submitData.isRead = true;
      else if (submitData.isRead === "false") submitData.isRead = false;
      else delete submitData.isRead;

      if (submitData.isActive === "true") submitData.isActive = true;
      else if (submitData.isActive === "false") submitData.isActive = false;
      else delete submitData.isActive;

      emit("confirm", submitData);
    }
  });
};

const handleReset = () => {
  formRef.value?.resetFields();
  Object.assign(formData, {
    title: "",
    type: "",
    priority: "",
    targetType: "",
    status: "",
    isRead: "",
    isActive: "",
    isTrashed: "no",
    dateRange: []
  });
};

// Watch for data changes
watch(
  () => props.data,
  newData => {
    if (newData) {
      Object.assign(formData, {
        title: newData.title || "",
        type: newData.type || "",
        priority: newData.priority || "",
        targetType: newData.targetType || "",
        status: newData.status || "",
        isRead: newData.isRead !== undefined ? String(newData.isRead) : "",
        isActive:
          newData.isActive !== undefined ? String(newData.isActive) : "",
        isTrashed: newData.isTrashed || "no",
        dateRange: newData.dateRange || []
      });
    }
  },
  { immediate: true, deep: true }
);
</script>

<template>
  <el-drawer
    :model-value="visible"
    :title="$t('Filter Notifications')"
    direction="rtl"
    size="400px"
    @close="handleClose"
  >
    <template #default>
      <el-form
        ref="formRef"
        :model="formData"
        label-width="120px"
        label-position="top"
        class="px-4"
      >
        <el-form-item :label="$t('Notification Title')" prop="title">
          <el-input
            v-model="formData.title"
            :placeholder="$t('Search by notification title')"
            clearable
          />
        </el-form-item>

        <el-form-item :label="$t('Type')" prop="type">
          <el-select
            v-model="formData.type"
            :placeholder="$t('Please select notification type')"
            clearable
            class="w-full"
          >
            <el-option
              v-for="option in typeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('Priority')" prop="priority">
          <el-select
            v-model="formData.priority"
            :placeholder="$t('Please select priority')"
            clearable
            class="w-full"
          >
            <el-option
              v-for="option in priorityOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('Target Type')" prop="targetType">
          <el-select
            v-model="formData.targetType"
            :placeholder="$t('Please select target type')"
            clearable
            class="w-full"
          >
            <el-option
              v-for="option in targetTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('Status')" prop="status">
          <el-select
            v-model="formData.status"
            :placeholder="$t('Please select status')"
            clearable
            class="w-full"
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('Read Status')" prop="isRead">
          <el-select
            v-model="formData.isRead"
            :placeholder="$t('Please select read status')"
            clearable
            class="w-full"
          >
            <el-option
              v-for="option in isReadOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('Active Status')" prop="isActive">
          <el-select
            v-model="formData.isActive"
            :placeholder="$t('Please select active status')"
            clearable
            class="w-full"
          >
            <el-option
              v-for="option in isActiveOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('Trash Status')" prop="isTrashed">
          <el-select
            v-model="formData.isTrashed"
            :placeholder="$t('Please select trash status')"
            class="w-full"
          >
            <el-option
              v-for="option in trashOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('Date Range')" prop="dateRange">
          <el-date-picker
            v-model="formData.dateRange"
            type="datetimerange"
            :range-separator="$t('To')"
            :start-placeholder="$t('Start date')"
            :end-placeholder="$t('End date')"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="w-full"
          />
        </el-form-item>
      </el-form>
    </template>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <el-button @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button @click="handleClose">
          {{ $t("Cancel") }}
        </el-button>
        <el-button type="primary" @click="handleConfirm">
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>
