import { reactive, ref } from "vue";
import {
  getNotifications,
  createNotification,
  updateNotificationById,
  deleteNotificationById,
  bulkDeleteNotifications,
  bulkDestroyNotifications,
  destroyNotificationById,
  restoreNotificationById,
  bulkRestoreNotifications,
  sendNotification,
  markAsRead,
  markAsUnread,
  bulkMarkAsRead,
  bulkMarkAsUnread,
  getNotificationStats
} from "../utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import type { NotificationFilterProps } from "@/views/notification/utils/type";

export function useNotificationHook() {
  // Reactive state
  const loading = ref(false);
  const filterRef = reactive<NotificationFilterProps>({
    isTrashed: "no"
  });
  const pagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "created_at", sortOrder: "desc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({ 
    status: "draft",
    type: "info",
    priority: "medium",
    targetType: "all",
    isActive: true
  });
  const notificationFormRef = ref();
  const stats = ref({});

  // API Handlers
  const fnGetNotifications = async () => {
    loading.value = true;
    try {
      const response = await getNotifications({
        ...filterRef,
        order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
        page: pagination.currentPage,
        limit: pagination.pageSize
      });
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;
    } catch (e) {
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  const fnGetNotificationStats = async () => {
    try {
      const response = await getNotificationStats();
      stats.value = useConvertKeyToCamel(response.data);
    } catch (e) {
      console.error("Get notification stats error:", e);
    }
  };

  const fnHandleCreateNotification = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createNotification(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetNotifications();
        await fnGetNotificationStats();
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateNotification = async (id: number, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateNotificationById(id, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetNotifications();
        await fnGetNotificationStats();
        return true;
      }
      message(response?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleDelete = async (id: number) => {
    try {
      loading.value = true;
      const response = await deleteNotificationById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetNotifications();
        await fnGetNotificationStats();
        return true;
      }
      message(response?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkDeleteNotifications({ ids });
      if (response.success) {
        message(response.message || $t("Bulk delete successful"), {
          type: "success"
        });
        await fnGetNotifications();
        await fnGetNotificationStats();
        return true;
      }
      message(response?.message || $t("Bulk delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Bulk delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Send notification
  const fnSendNotification = async (id: number) => {
    try {
      const response = await sendNotification(id);
      if (response.success) {
        message(response.message || $t("Send successful"), { type: "success" });
        await fnGetNotifications();
        await fnGetNotificationStats();
      } else {
        message(response.message || $t("Send failed"), { type: "error" });
      }
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Send failed"),
        { type: "error" }
      );
    }
  };

  // Mark as read/unread
  const fnMarkAsRead = async (id: number) => {
    try {
      const response = await markAsRead(id);
      if (response.success) {
        message(response.message || $t("Mark as read successful"), { type: "success" });
        await fnGetNotifications();
        await fnGetNotificationStats();
      } else {
        message(response.message || $t("Mark as read failed"), { type: "error" });
      }
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Mark as read failed"),
        { type: "error" }
      );
    }
  };

  const fnBulkMarkAsRead = async (ids: number[]) => {
    try {
      const response = await bulkMarkAsRead({ ids });
      if (response.success) {
        message(response.message || $t("Bulk mark as read successful"), { type: "success" });
        await fnGetNotifications();
        await fnGetNotificationStats();
      } else {
        message(response.message || $t("Bulk mark as read failed"), { type: "error" });
      }
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Bulk mark as read failed"),
        { type: "error" }
      );
    }
  };

  // Table Event Handlers
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = ({ prop, order }: any) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetNotifications();
  };

  const fnHandlePageChange = (val: number) => {
    pagination.currentPage = val;
    fnGetNotifications();
  };

  const fnHandleSizeChange = (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    fnGetNotifications();
  };

  // UI Action Handlers
  const handleDelete = (row: any) => {
    ElMessageBox.confirm(
      $t("Are you sure to delete this item?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    )
      .then(() => {
        fnHandleDelete(row.id);
      })
      .catch(() => {});
  };

  const handleBulkDelete = () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }
    ElMessageBox.confirm(
      $t("Are you sure to delete selected items?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    )
      .then(() => {
        const ids = multipleSelection.value.map((item: any) => item.id);
        fnHandleBulkDelete(ids);
      })
      .catch(() => {});
  };

  const handleDestroy = (row: any) => {
    ElMessageBox.confirm(
      $t("Are you sure to move this item to trash?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    )
      .then(async () => {
        try {
          loading.value = true;
          const response = await destroyNotificationById(row.id);
          if (response.success) {
            message(response.message || $t("Move to trash successful"), {
              type: "success"
            });
            await fnGetNotifications();
            await fnGetNotificationStats();
          } else {
            message(response.message || $t("Move to trash failed"), {
              type: "error"
            });
          }
        } catch (error) {
          message(
            error.response?.data?.message || error?.message || $t("Move to trash failed"),
            { type: "error" }
          );
        } finally {
          loading.value = false;
        }
      })
      .catch(() => {});
  };

  const handleBulkDestroy = () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }
    ElMessageBox.confirm(
      $t("Are you sure to move selected items to trash?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    )
      .then(async () => {
        try {
          loading.value = true;
          const ids = multipleSelection.value.map((item: any) => item.id);
          const response = await bulkDestroyNotifications({ ids });
          if (response.success) {
            message(response.message || $t("Bulk move to trash successful"), {
              type: "success"
            });
            await fnGetNotifications();
            await fnGetNotificationStats();
          } else {
            message(response.message || $t("Bulk move to trash failed"), {
              type: "error"
            });
          }
        } catch (error) {
          message(
            error.response?.data?.message || error?.message || $t("Bulk move to trash failed"),
            { type: "error" }
          );
        } finally {
          loading.value = false;
        }
      })
      .catch(() => {});
  };

  const handleRestore = (row: any) => {
    ElMessageBox.confirm(
      $t("Are you sure to restore this item?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    )
      .then(async () => {
        try {
          loading.value = true;
          const response = await restoreNotificationById(row.id);
          if (response.success) {
            message(response.message || $t("Restore successful"), {
              type: "success"
            });
            await fnGetNotifications();
            await fnGetNotificationStats();
          } else {
            message(response.message || $t("Restore failed"), {
              type: "error"
            });
          }
        } catch (error) {
          message(
            error.response?.data?.message || error?.message || $t("Restore failed"),
            { type: "error" }
          );
        } finally {
          loading.value = false;
        }
      })
      .catch(() => {});
  };

  const handleBulkRestore = () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }
    ElMessageBox.confirm(
      $t("Are you sure to restore selected items?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    )
      .then(async () => {
        try {
          loading.value = true;
          const ids = multipleSelection.value.map((item: any) => item.id);
          const response = await bulkRestoreNotifications({ ids });
          if (response.success) {
            message(response.message || $t("Bulk restore successful"), {
              type: "success"
            });
            await fnGetNotifications();
            await fnGetNotificationStats();
          } else {
            message(response.message || $t("Bulk restore failed"), {
              type: "error"
            });
          }
        } catch (error) {
          message(
            error.response?.data?.message || error?.message || $t("Bulk restore failed"),
            { type: "error" }
          );
        } finally {
          loading.value = false;
        }
      })
      .catch(() => {});
  };

  const handleSend = (row: any) => {
    ElMessageBox.confirm(
      $t("Are you sure to send this notification?"),
      $t("Warning"),
      {
        confirmButtonText: $t("Confirm"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    )
      .then(() => {
        fnSendNotification(row.id);
      })
      .catch(() => {});
  };

  const handleMarkAsRead = (row: any) => {
    fnMarkAsRead(row.id);
  };

  const handleBulkMarkAsRead = () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items"), { type: "warning" });
      return;
    }
    const ids = multipleSelection.value.map((item: any) => item.id);
    fnBulkMarkAsRead(ids);
  };

  // Form Handlers
  const handleSubmit = async (formData: FieldValues) => {
    const isEdit = !!drawerValues.value?.id;
    const success = isEdit
      ? await fnHandleUpdateNotification(drawerValues.value.id, formData)
      : await fnHandleCreateNotification(formData);

    if (success) {
      drawerVisible.value = false;
      drawerValues.value = {
        status: "draft",
        type: "info",
        priority: "medium",
        targetType: "all",
        isActive: true
      };
    }
  };

  const handleFilter = async (formData: FieldValues) => {
    Object.assign(filterRef, formData);
    pagination.currentPage = 1;
    await fnGetNotifications();
    filterVisible.value = false;
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    notificationFormRef,
    stats,

    // API Handlers
    fnGetNotifications,
    fnGetNotificationStats,
    fnHandleCreateNotification,
    fnHandleUpdateNotification,
    fnHandleDelete,
    fnHandleBulkDelete,
    fnSendNotification,
    fnMarkAsRead,
    fnBulkMarkAsRead,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,
    handleSend,
    handleMarkAsRead,
    handleBulkMarkAsRead,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
