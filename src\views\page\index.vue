<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { usePageHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";

// Lazy load components
const PageDrawerForm = defineAsyncComponent(
  () => import("./components/PageDrawerForm.vue")
);

const PageFilterForm = defineAsyncComponent(
  () => import("./components/PageFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetPages,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  pageFormRef,
  handleSubmit,
  handleFilter,
  handleBulkDestroy,
  handleBulkRestore,
  handleDestroy,
  handleRestore
} = usePageHook();

const handleEdit = (row: any) => {
  drawerValues.value = clone(row, true);
  drawerVisible.value = true;
};

onMounted(() => {
  nextTick(() => {
    fnGetPages();
  });
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="!w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('Page Management')"
        :columns="columns"
        @refresh="fnGetPages"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('page.create')"
              @click="
                () => {
                  drawerVisible = true;
                }
              "
            >
              <IconifyIconOnline icon="ep:plus" />
            </el-button>
          </el-tooltip>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown trigger="click">
                <el-button type="primary" size="small" text>
                  {{ $t("More") }}
                  <IconifyIconOnline icon="ep:arrow-down" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <template v-if="filterRef.isTrashed === 'no'">
                      <el-dropdown-item
                        :disabled="!hasAuth('page.update')"
                        @click="handleEdit(row)"
                      >
                        <IconifyIconOnline
                          icon="ep:edit"
                          class="text-primary"
                        />
                        <span class="ml-2">
                          {{ $t("Edit") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!hasAuth('page.delete')"
                        @click="handleDelete(row.id)"
                      >
                        <IconifyIconOnline
                          icon="ep:delete"
                          class="text-red-600"
                        />
                        <span class="ml-2">
                          {{ $t("Move to Trash") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                    <template v-else>
                      <el-dropdown-item
                        :disabled="!hasAuth('page.restore')"
                        @click="handleRestore(row.id)"
                      >
                        <IconifyIconOnline
                          icon="ep:refresh-left"
                          class="text-green-600"
                        />
                        <span class="ml-2">
                          {{ $t("Restore") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!hasAuth('page.force-delete')"
                        @click="handleDestroy(row.id)"
                      >
                        <IconifyIconOnline
                          icon="tabler:trash-x"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Delete") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              />
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <PageDrawerForm
      ref="pageFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        () => {
          pageFormRef.value?.resetForm();
          drawerValues = { status: 'draft', publishedAt: null };
        }
      "
    />

    <PageFilterForm
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="() => { filterRef = { isTrashed: 'no' }; fnGetPages(); }"
    />
  </div>
</template>
