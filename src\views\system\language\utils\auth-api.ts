import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/system/language/utils/type";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getLanguages = (params?: object) => {
  return http.request<Result>("get", "/api/auth/languages", {
    params
  });
};

export const getLanguageById = (id: number) => {
  return http.request<Result>("get", `/api/auth/languages/${id}`);
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createLanguage = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/languages", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateLanguageById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/languages/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteLanguageById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/languages/${id}`);
};

export const bulkDeleteLanguages = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/languages/bulk-delete", {
    data
  });
};

/*
 ***************************
 *   Hard Delete Operations
 ***************************
 */
export const destroyLanguageById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/languages/${id}`);
};

export const bulkDestroyLanguages = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/languages/bulk-destroy", {
    data
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreLanguageById = (id: number) => {
  return http.request<Result>("put", `/api/auth/languages/${id}/restore`);
};

export const bulkRestoreLanguages = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/languages/bulk-restore", {
    data
  });
};

/*
 ***************************
 *   Dropdown Operations
 ***************************
 */
export const dropdownLanguages = () => {
  return http.request<Result>("get", "/api/auth/languages/dropdown");
};
