<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);
const formRef = ref();

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Name")),
    prop: "name",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input role name"),
        trigger: ["blur", "change"]
      }
    ],
    fieldProps: {
      placeholder: $t("Please input role name")
    }
  },
  {
    label: computed(() => $t("Display Name")),
    prop: "displayName",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Please input display name")
    }
  },
  {
    label: computed(() => $t("Guard Name")),
    prop: "guardName",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Please input guard name")
    }
  },
  {
    label: computed(() => $t("Description")),
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Please input description"),
      rows: 3
    }
  },
  {
    label: computed(() => $t("Priority")),
    prop: "priority",
    valueType: "input-number",
    fieldProps: {
      placeholder: $t("Please input priority"),
      min: 0,
      max: 999
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Please select status")
    },
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ]
  },
  {
    label: computed(() => $t("System Role")),
    prop: "isSystem",
    valueType: "switch",
    fieldProps: {
      activeText: $t("Yes"),
      inactiveText: $t("No")
    }
  },
  {
    label: computed(() => $t("Permissions")),
    prop: "permissions",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Please select permissions"),
      multiple: true,
      filterable: true,
      clearable: true
    },
    options: [] // Will be populated from API
  }
];

const handleSubmit = async (values: FieldValues) => {
  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="50%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
