<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent } from "vue";
import { useRoleHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";

const RoleDrawerForm = defineAsyncComponent(
  () => import("./components/RoleDrawerForm.vue")
);

const RoleFilterForm = defineAsyncComponent(
  () => import("./components/RoleFilterForm.vue")
);

const tableRef = ref();

const {
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  sort,
  rolesDropdown,
  handleBulkDelete,
  handleDelete,
  handlePermanentDelete,
  handleBulkPermanentDelete,
  handleRestore,
  handleBulkRestore,
  fnGetRoles,
  fnGetRolesDropdown,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  filterVisible,
  drawerVisible,
  drawerValues,
  roleFormRef,
  handleSubmit,
  handleFilter,
  handleEdit
} = useRoleHook();

onMounted(() => {
  fnGetRoles();
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        border
        :title="$t('Role Management')"
        :columns="columns"
        @refresh="fnGetRoles"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-button
            v-if="hasAuth('create-roles')"
            type="primary"
            @click="drawerVisible = true"
          >
            <IconifyIconOnline icon="ri:add-line" />
            {{ $t("Create") }}
          </el-button>
          <el-button
            v-if="hasAuth('delete-roles') && multipleSelection.length > 0"
            type="danger"
            @click="() => handleBulkDelete()"
          >
            <IconifyIconOnline icon="ri:delete-bin-line" />
            {{ $t("Delete") }}
          </el-button>
          <el-button
            v-if="
              hasAuth('force-delete-roles') &&
              multipleSelection.length > 0 &&
              filterRef.isTrashed === 'yes'
            "
            type="danger"
            @click="() => handleBulkPermanentDelete()"
          >
            <IconifyIconOnline icon="ri:delete-bin-2-line" />
            {{ $t("Permanent Delete") }}
          </el-button>
          <el-button
            v-if="
              hasAuth('restore-roles') &&
              multipleSelection.length > 0 &&
              filterRef.isTrashed === 'yes'
            "
            type="success"
            @click="() => handleBulkRestore()"
          >
            <IconifyIconOnline icon="ri:refresh-line" />
            {{ $t("Restore") }}
          </el-button>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            border
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown trigger="click">
                <el-button type="primary" size="small" text>
                  {{ $t("Operations") }}
                  <IconifyIconOnline
                    :icon="'ri:arrow-down-s-line'"
                    class="ml-1"
                  />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-if="hasAuth('edit-roles') && !row.deletedAt"
                      @click="handleEdit(row)"
                    >
                      <IconifyIconOnline :icon="'ri:edit-line'" class="mr-2" />
                      {{ $t("Edit") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="hasAuth('delete-roles') && !row.deletedAt"
                      @click="handleDelete(row)"
                    >
                      <IconifyIconOnline
                        :icon="'ri:delete-bin-line'"
                        class="mr-2"
                      />
                      {{ $t("Delete") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="hasAuth('force-delete-roles') && row.deletedAt"
                      @click="handlePermanentDelete(row)"
                    >
                      <IconifyIconOnline
                        :icon="'ri:delete-bin-2-line'"
                        class="mr-2"
                      />
                      {{ $t("Permanent Delete") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="hasAuth('restore-roles') && row.deletedAt"
                      @click="handleRestore(row)"
                    >
                      <IconifyIconOnline
                        :icon="'ri:refresh-line'"
                        class="mr-2"
                      />
                      {{ $t("Restore") }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>
    <RoleDrawerForm
      ref="roleFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        () => {
          roleFormRef?.resetForm();
          drawerValues = {};
        }
      "
    />
    <RoleFilterForm
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="
        () => {
          filterRef = { isTrashed: 'no' };
          fnGetRoles();
        }
      "
    />
  </div>
</template>

<style lang="scss" scoped>
.main {
  @apply p-4;
}
</style>
