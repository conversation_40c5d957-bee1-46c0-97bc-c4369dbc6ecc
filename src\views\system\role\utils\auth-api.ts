import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getRoles = (params?: any) => {
  return http.request<Result>("get", "/api/v1/auth/roles", { params });
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createRole = (data: any) => {
  return http.request<Result>("post", "/api/v1/auth/roles", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateRoleById = (id: number, data: any) => {
  return http.request<Result>("put", `/api/v1/auth/roles/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteRole = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/roles/${id}`);
};

export const bulkDeleteRoles = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/v1/auth/roles/bulk", {
    data: useConvertKeyToSnake(data)
  });
};
