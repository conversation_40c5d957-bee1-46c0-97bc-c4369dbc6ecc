import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "name",
    align: "left",
    sortable: "custom",
    width: 210,
    headerRenderer: () => $t("Name")
  },
  {
    prop: "displayName",
    sortable: false,
    align: "left",
    width: 260,
    headerRenderer: () => $t("Display name")
  },
  {
    prop: "description",
    align: "left",
    minWidth: 180,
    headerRenderer: () => $t("Description")
  },
  {
    prop: "createdAt",
    sortable: true,
    align: "left",
    width: 130,
    formatter: ({ createdAt }) =>
      dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss"),
    headerRenderer: () => $t("Created at")
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
