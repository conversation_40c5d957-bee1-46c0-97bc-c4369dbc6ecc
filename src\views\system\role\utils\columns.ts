import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "name",
    align: "left",
    sortable: "custom",
    width: 210,
    headerRenderer: () => $t("Name")
  },
  {
    prop: "displayName",
    sortable: false,
    align: "left",
    width: 200,
    headerRenderer: () => $t("Display name")
  },
  {
    prop: "guardName",
    sortable: "custom",
    align: "left",
    width: 120,
    headerRenderer: () => $t("Guard Name")
  },
  {
    prop: "status",
    sortable: "custom",
    align: "center",
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const status = row.status || 'active';
      const statusConfig = {
        active: { color: '#67c23a', text: $t('Active') },
        inactive: { color: '#f56c6c', text: $t('Inactive') }
      };
      const config = statusConfig[status] || statusConfig.active;
      return (
        <el-tag type={status === 'active' ? 'success' : 'danger'} size="small">
          {config.text}
        </el-tag>
      );
    }
  },
  {
    prop: "priority",
    sortable: "custom",
    align: "center",
    width: 80,
    headerRenderer: () => $t("Priority")
  },
  {
    prop: "isSystem",
    sortable: "custom",
    align: "center",
    width: 100,
    headerRenderer: () => $t("System Role"),
    cellRenderer: ({ row }) => {
      return (
        <el-tag type={row.isSystem ? 'warning' : 'info'} size="small">
          {row.isSystem ? $t('Yes') : $t('No')}
        </el-tag>
      );
    }
  },
  {
    prop: "description",
    align: "left",
    minWidth: 150,
    headerRenderer: () => $t("Description")
  },
  {
    prop: "createdAt",
    sortable: true,
    align: "left",
    width: 130,
    formatter: ({ createdAt }) =>
      dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss"),
    headerRenderer: () => $t("Created at")
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
