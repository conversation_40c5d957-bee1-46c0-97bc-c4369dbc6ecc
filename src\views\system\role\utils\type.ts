export type Permission = {
  id: number;
  name: string;
  guardName: string;
  description?: string;
  moduleName?: string;
  createdAt: string;
  updatedAt: string;
};

export type Role = {
  id: number;
  name: string;
  guardName: string;
  displayName?: string;
  description?: string;
  priority?: number;
  isSystem?: boolean;
  status?: "active" | "inactive";
  permissions?: Permission[];
  usersCount?: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
};

export type FormItemProps = {
  id?: number | null;
  name?: string;
  guardName?: string;
  displayName?: string;
  description?: string;
  priority?: number;
  isSystem?: boolean;
  status?: "active" | "inactive";
  permissions?: number[];
  [key: string]: any;
};

export type RoleFilterProps = {
  name?: string;
  guardName?: string;
  status?: "active" | "inactive";
  moduleName?: string;
  sortBy?: string;
  sortDirection?: "asc" | "desc";
  isTrashed?: string;
  [key: string]: any;
};

export type RoleDropdownItem = {
  id: number;
  name: string;
  displayName?: string;
};
