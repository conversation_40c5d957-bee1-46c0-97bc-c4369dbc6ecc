## Field Meanings

| Field             | Description                                                                                                       |
| :---------------- | :---------------------------------------------------------------------------------------------------------------- |
| `menuType`        | Menu type (`0` for menu, `1` for iframe, `2` for external link, `3` for button)                                    |
| `parentId`        |                                                                                                                    |
| `title`           | Menu name (supports both internationalized and non-internationalized names; if using the internationalized format, it must be added to the `locales` folder in the root directory) |
| `name`            | Route name (must be unique and consistent with the `name` wrapped in `defineOptions` in the corresponding route component) |
| `path`            | Route path                                                                                                        |
| `component`       | Component path (if the `component` path is provided, `path` can be arbitrary; otherwise, the `component` path will match `path`) |
| `rank`            | Menu order (only the `home` route can have a `rank` of `0`; therefore, the backend must start returning `rank` values from non-zero) |
| `redirect`        | Route redirection                                                                                                 |
| `icon`            | Menu icon                                                                                                         |
| `extraIcon`       | Right-side icon                                                                                                   |
| `enterTransition` | Enter animation (page load animation)                                                                             |
| `leaveTransition` | Leave animation (page load animation)                                                                             |
| `activePath`      | Active menu (used to activate a menu, mainly for routes with `query` or `params`; if these routes have `showLink: false` and are not displayed in the menu, no menu will be highlighted, but by setting `activePath` to the path of the menu to be activated, it will be highlighted) |
| `auths`           | Authorization identifier (button-level permissions)                                                               |
| `frameSrc`        | Link address (URL for the embedded `iframe`)                                                                      |
| `frameLoading`    | Loading animation (whether to enable the initial load animation for the embedded `iframe`)                        |
| `keepAlive`       | Cache page (whether to cache the route page; if enabled, the overall state of the page is preserved, but the state will be cleared after refreshing) |
| `hiddenTag`       | Tab (whether to prohibit the current menu name or custom information from being added to the tab)                 |
| `fixedTag`        | Fixed tab (whether to fix the current menu name in the tab and make it unclosable)                                |
| `showLink`        | Menu (whether to display this menu)                                                                               |
| `showParent`      | Parent menu (whether to display the parent menu)                                                                  |
