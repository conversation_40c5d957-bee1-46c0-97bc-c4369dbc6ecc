<script setup lang="ts">
import { ref, computed } from "vue";
import { transformI18n } from "@/plugins/i18n";
import type { FilterProps } from "@/api/types/sidebar";
import { PlusDrawerForm, type PlusColumn } from "plus-pro-components";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FilterProps;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FilterProps): void;
  (e: "submit", values: FilterProps): void;
  (e: "reset"): void;
}>();

const formRef = ref();
const loading = ref(false);

const formColumns: PlusColumn[] = [
  {
    label: computed(() => transformI18n("Title")),
    prop: "title",
    valueType: "input",
    fieldProps: {
      placeholder: transformI18n("Enter title"),
      clearable: true
    },
    colProps: { span: 24 }
  }
];

const handleSubmit = async (values: FilterProps) => {
  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    loading.value = false;
  }
};

const handleReset = () => {
  resetForm();
  emit("reset");
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="35%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ transformI18n("Filter Sidebar") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ transformI18n("Reset") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:filter-3-line')"
          @click="handleSubmit(values)"
        >
          {{ transformI18n("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
