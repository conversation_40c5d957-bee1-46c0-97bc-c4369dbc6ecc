<script setup lang="ts">
import { ref, computed, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useSidebarHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import PureTable from "@pureadmin/table";

// Lazy load components
const SidebarDrawerForm = defineAsyncComponent(
  () => import("./components/SidebarDrawerForm.vue")
);

const SidebarFilterForm = defineAsyncComponent(
  () => import("./components/SidebarFilterForm.vue")
);

const iconClass = computed(() => {
  return [
    "w-[22px]",
    "h-[22px]",
    "flex",
    "justify-center",
    "items-center",
    "outline-none",
    "rounded-[4px]",
    "cursor-pointer",
    "transition-colors",
    "hover:bg-[#0000000f]",
    "dark:hover:bg-[#ffffff1f]",
    "dark:hover:text-[#ffffffd9]"
  ];
});

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  records,
  multipleSelection,
  // Event handlers
  handleDelete,
  fnGetSidebars,
  fnHandleSelectionChange,
  fnHandleSortChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  sidebarFormRef,
  handleSubmit,
  handleFilter
} = useSidebarHook();

onMounted(() => {
  nextTick(() => {
    fnGetSidebars();
  });
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        border
        :title="$t('Sidebar Management')"
        :columns="columns"
        :isExpandAll="false"
        :tableRef="tableRef?.getTableRef()"
        @refresh="fnGetSidebars"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <TableButtons
            :multiple-selection="multipleSelection"
            :is-trashed="'disabled'"
            :create-permission="'create-sidebars'"
            @create="drawerVisible = true"
          />
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            row-key="id"
            adaptive
            border
            :loading="loading"
            :size="size"
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <div class="flex items-center gap-2">
                <el-button
                  v-if="hasAuth('sidebar:update')"
                  type="primary"
                  size="small"
                  :icon="useRenderIcon('ep:edit')"
                  @click="
                    () => {
                      drawerValues = clone(row);
                      drawerVisible = true;
                    }
                  "
                >
                  {{ $t('Edit') }}
                </el-button>
                <el-button
                  v-if="hasAuth('sidebar:delete')"
                  type="danger"
                  size="small"
                  :icon="useRenderIcon('ep:delete')"
                  @click="handleDelete(row)"
                >
                  {{ $t('Delete') }}
                </el-button>
              </div>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <SidebarDrawerForm
      ref="sidebarFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        sidebarFormRef.value?.resetForm();
        drawerValues = { menuType: 0 };
      "
    />

    <SidebarFilterForm
      ref="filterFormRef"
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="
        filterRef = {};
        fnGetSidebars();
      "
    />
  </div>
</template>

<style lang="scss" scoped>
.main {
  @apply p-4;
}
</style>
