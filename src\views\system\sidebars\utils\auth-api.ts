import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/system/sidebars/utils/type";

/* ***************************
 * API Data Fetching
 *************************** */

export const getSidebars = (params?: object) => {
  return http.request<Result>("get", "/api/auth/sidebars", {
    params
  });
};

export const getSidebarById = (id: number) => {
  return http.request<Result>("get", `/api/auth/sidebars/${id}`);
};

/* ***************************
 * API CRUD Operations
 *************************** */

export const createSidebar = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/sidebars", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateSidebarById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/sidebars/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/* ***************************
 * Delete handlers and actions
 *************************** */

export const deleteSidebar = (id: number) => {
  return http.request<Result>("delete", `/api/auth/sidebars/${id}`);
};

export const bulkDeleteSidebars = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/sidebars/bulk-delete", {
    data
  });
};

/* ***************************
 * Destroy handlers and actions
 *************************** */

export const destroySidebar = (id: number) => {
  return http.request<Result>("delete", `/api/auth/sidebars/${id}/force-delete`);
};

export const bulkDestroySidebars = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/sidebars/bulk-destroy", {
    data
  });
};

/* ***************************
 * Restore handlers and actions
 *************************** */

export const restoreSidebar = (id: number) => {
  return http.request<Result>("put", `/api/auth/sidebars/${id}/restore`);
};

export const bulkRestoreSidebars = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/sidebars/bulk-restore", {
    data
  });
};

/* ***************************
 * Dropdown/Select Options
 *************************** */

export const dropdownSidebars = () => {
  return http.request<Result>("get", "/api/auth/sidebars/dropdown");
};
