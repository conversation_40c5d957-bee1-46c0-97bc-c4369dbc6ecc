export interface FormItemProps {
  id?: number;
  name: string;
  title: string;
  icon?: string;
  path?: string;
  component?: string;
  redirect?: string;
  meta?: any;
  parent_id?: number;
  sort?: number;
  is_hidden?: boolean;
  is_keepalive?: boolean;
  is_affix?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface SidebarFilterProps {
  name?: string;
  title?: string;
  is_hidden?: boolean;
  isTrashed?: boolean;
}
