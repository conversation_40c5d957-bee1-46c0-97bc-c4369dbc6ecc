<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { dropdownLanguages } from "@/views/language/utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  groups: any[];
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);

const formRef = ref();
const languages = ref([]);

onMounted(async () => {
  try {
    const { data } = await dropdownLanguages();
    languages.value = useConvertKeyToCamel(data);
  } catch (error) {
    console.error("Failed to load languages:", error);
  }
});

const groupOptions = computed(() => {
  return props.groups.map(group => ({
    label: group.name || group,
    value: group.name || group
  }));
});

const languageOptions = computed(() => {
  return languages.value.map((lang: any) => ({
    label: `${lang.name} (${lang.code})`,
    value: lang.code
  }));
});

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Translation Key")),
    prop: "key",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input translation key"),
        trigger: ["blur"]
      },
      {
        min: 2,
        max: 255,
        message: $t("Length must be between 2 and 255 characters"),
        trigger: ["blur"]
      },
      {
        pattern: /^[a-zA-Z0-9._-]+$/,
        message: $t("Key can only contain letters, numbers, dots, underscores and hyphens"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    },
    colProps: { span: 15 }
  },
  {
    label: computed(() => $t("Language")),
    prop: "languageCode",
    valueType: "select",
    required: true,
    options: languageOptions,
    rules: [
      {
        required: true,
        message: $t("Please select language"),
        trigger: ["change"]
      }
    ],
    fieldProps: {
      placeholder: "",
      clearable: false
    },
    colProps: { span: 9 }
  },
  {
    label: computed(() => $t("Translation Value")),
    prop: "value",
    valueType: "textarea",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input translation value"),
        trigger: ["blur"]
      },
      {
        max: 5000,
        message: $t("Translation value cannot exceed 5000 characters"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: $t("Enter translation value"),
      showWordLimit: true,
      autosize: { minRows: 3, maxRows: 8 }
    }
  },
  {
    label: computed(() => $t("Group")),
    prop: "group",
    valueType: "select",
    options: groupOptions,
    fieldProps: {
      placeholder: $t("Select or enter group"),
      filterable: true,
      allowCreate: true
    }
  },
  {
    label: computed(() => $t("Description")),
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Enter description (optional)"),
      showWordLimit: true,
      autosize: { minRows: 2, maxRows: 4 }
    }
  },
  {
    label: computed(() => $t("Is Plural")),
    prop: "isPlural",
    valueType: "switch",
    fieldProps: {
      activeText: $t("Yes"),
      inactiveText: $t("No")
    }
  },
  {
    label: computed(() => $t("Is Active")),
    prop: "isActive",
    valueType: "switch",
    fieldProps: {
      activeText: $t("Active"),
      inactiveText: $t("Inactive")
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    required: true,
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ],
    fieldProps: {
      placeholder: $t("Select status")
    }
  }
];

const handleSubmit = async (values: FieldValues) => {
  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="50%"
    :closeOnClickModal="false"
    :closeOnPressEscape="false"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: true,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @confirm="handleSubmit"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ values.id ? $t("Edit Translation") : $t("Add Translation") }}
        </span>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style lang="scss" scoped>
.custom-group-header {
  @apply flex items-center gap-2;
}
</style>
