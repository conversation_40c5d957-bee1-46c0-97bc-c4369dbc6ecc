<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref, onMounted } from "vue";
import { dropdownLanguages } from "@/views/language/utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  groups: any[];
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();
const languages = ref([]);

onMounted(async () => {
  try {
    const { data } = await dropdownLanguages();
    languages.value = useConvertKeyToCamel(data);
  } catch (error) {
    console.error("Failed to load languages:", error);
  }
});

const groupOptions = computed(() => {
  const allOption = { label: $t("All"), value: "" };
  const groupOpts = props.groups.map(group => ({
    label: group.name || group,
    value: group.name || group
  }));
  return [allOption, ...groupOpts];
});

const languageOptions = computed(() => {
  const allOption = { label: $t("All"), value: "" };
  const langOpts = languages.value.map((lang: any) => ({
    label: `${lang.name} (${lang.code})`,
    value: lang.code
  }));
  return [allOption, ...langOpts];
});

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Translation Key")),
    prop: "key",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Language")),
    prop: "languageCode",
    valueType: "select",
    options: languageOptions,
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Translation Value")),
    prop: "value",
    valueType: "input",
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Group")),
    prop: "group",
    valueType: "select",
    options: groupOptions,
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Is Plural")),
    prop: "isPlural",
    valueType: "select",
    options: [
      { label: $t("All"), value: "" },
      { label: $t("Yes"), value: true },
      { label: $t("No"), value: false }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Is Active")),
    prop: "isActive",
    valueType: "select",
    options: [
      { label: $t("All"), value: "" },
      { label: $t("Active"), value: true },
      { label: $t("Inactive"), value: false }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    options: [
      { label: $t("All"), value: "" },
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Trashed")),
    prop: "isTrashed",
    valueType: "select",
    options: [
      { label: $t("All"), value: "" },
      { label: $t("Yes"), value: "yes" },
      { label: $t("No"), value: "no" }
    ],
    fieldProps: {
      placeholder: "",
      clearable: false
    }
  }
];

const handleSubmit = async () => {
  try {
    loading.value = true;
    emit("submit", props.values);
  } catch (error) {
    console.error("Filter submission failed:", error);
  } finally {
    loading.value = false;
  }
};

const handleReset = () => {
  emit("reset");
};
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style lang="scss" scoped>
.custom-group-header {
  @apply flex items-center gap-2;
}

.custom-footer {
  @apply flex justify-end gap-2;
}
</style>
