<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useTranslationHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";

// Lazy load components
const TranslationDrawerForm = defineAsyncComponent(
  () => import("./components/TranslationDrawerForm.vue")
);

const TranslationFilterForm = defineAsyncComponent(
  () => import("./components/TranslationFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  groups,
  multipleSelection,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetTranslations,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  translationFormRef,
  handleSubmit,
  handleFilter,
  handleBulkDestroy,
  handleBulkRestore,
  handleDestroy,
  handleRestore
} = useTranslationHook();

const handleEdit = (row: any) => {
  drawerValues.value = clone(row, true);
  drawerVisible.value = true;
};

onMounted(() => {
  nextTick(() => {
    fnGetTranslations();
  });
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="!w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('Translation Management')"
        :columns="columns"
        @refresh="fnGetTranslations"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('translation.create')"
              @click="
                () => {
                  drawerVisible = true;
                }
              "
            >
              <IconifyIconOnline
                :icon="
                  hasAuth('translation.create')
                    ? 'flat-color-icons:plus'
                    : 'icons8:plus'
                "
                width="18px"
              />
            </el-button>
          </el-tooltip>
          <template v-if="filterRef.isTrashed === 'yes'">
            <el-tooltip :content="$t('Restore')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length === 0 ||
                  (multipleSelection.length > 0 &&
                    !hasAuth('translation.restore'))
                "
                @click="handleBulkRestore"
              >
                <IconifyIconOnline
                  icon="tabler:restore"
                  width="18px"
                  :class="{ 'text-blue-600': multipleSelection.length > 0 }"
                />
              </el-button>
            </el-tooltip>
            <el-tooltip :content="$t('Bulk Delete')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length === 0 ||
                  (multipleSelection.length > 0 &&
                    !hasAuth('translation.force-delete'))
                "
                @click="handleBulkDelete"
              >
                <IconifyIconOnline
                  icon="tabler:trash-x-filled"
                  width="18px"
                  :class="{ 'text-red-700': multipleSelection.length > 0 }"
                />
              </el-button>
            </el-tooltip>
          </template>
          <template v-else>
            <el-tooltip
              v-if="filterRef.isTrashed === 'no'"
              :content="$t('Bulk Destroy')"
              placement="top"
            >
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length == 0 ||
                  (multipleSelection.length > 0 &&
                    !hasAuth('translation.destroy'))
                "
                @click="handleBulkDestroy"
              >
                <IconifyIconOnline
                  icon="tabler:trash"
                  width="18px"
                  :class="{ 'text-red-700': multipleSelection.length > 0 }"
                />
              </el-button>
            </el-tooltip>
          </template>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            border
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown split-button trigger="click" size="small">
                {{ $t("Action") }}
                <template #dropdown>
                  <el-dropdown-menu class="min-w-[130px]">
                    <el-dropdown-item disabled>
                      {{ $t("Action") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      :disabled="
                        filterRef.isTrashed == 'yes' ||
                        (filterRef.isTrashed == 'no' &&
                          !hasAuth('translation.update'))
                      "
                      divided
                      @click="
                        () => {
                          drawerValues = clone(row);
                          drawerVisible = true;
                        }
                      "
                    >
                      <IconifyIconOnline
                        icon="ant-design:edit-outlined"
                        class="text-blue-700"
                      />
                      <span class="ml-2">{{ $t("Edit") }}</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="filterRef.isTrashed === 'no'"
                      :disabled="!hasAuth('translation.destroy')"
                      @click="handleDestroy(row)"
                    >
                      <IconifyIconOnline
                        icon="tabler:trash"
                        class="text-red-800"
                      />
                      <span class="ml-2">
                        {{ $t("Destroy") }}
                      </span>
                    </el-dropdown-item>

                    <template v-if="filterRef.isTrashed == 'yes'">
                      <el-dropdown-item
                        :disabled="!hasAuth('translation.restore')"
                        @click="handleRestore(row)"
                      >
                        <IconifyIconOnline
                          icon="tabler:restore"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Restore") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!hasAuth('translation.force-delete')"
                        @click="handleDelete(row)"
                      >
                        <IconifyIconOnline
                          icon="tabler:trash-x"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Force Delete") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <!-- Translation Form Dialog -->
    <TranslationDrawerForm
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      :groups="groups"
      @submit="handleSubmit"
    />

    <!-- Filter Dialog -->
    <TranslationFilterForm
      ref="filterFormRef"
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      :groups="groups"
      @submit="handleFilter"
      @reset="
        () => {
          filterRef = {};
          handleFilter();
        }
      "
    />
  </div>
</template>

<style scoped lang="scss">
.main {
  margin: 0;
  padding: 12px;
  background: #fff;
  border-radius: 6px;
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
