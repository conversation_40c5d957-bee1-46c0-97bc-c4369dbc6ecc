import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/system/translation/utils/type";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getTranslations = (params?: object) => {
  return http.request<Result>("get", "/api/auth/translations", {
    params
  });
};

export const getTranslationById = (id: number) => {
  return http.request<Result>("get", `/api/auth/translations/${id}`);
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createTranslation = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/translations", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateTranslationById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/translations/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteTranslationById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/translations/${id}`);
};

export const bulkDeleteTranslations = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/translations/bulk-delete", {
    data
  });
};

/*
 ***************************
 *   Hard Delete Operations
 ***************************
 */
export const destroyTranslationById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/translations/${id}`);
};

export const bulkDestroyTranslations = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/translations/bulk-destroy", {
    data
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreTranslationById = (id: number) => {
  return http.request<Result>("put", `/api/auth/translations/${id}/restore`);
};

export const bulkRestoreTranslations = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/translations/bulk-restore", {
    data
  });
};

/*
 ***************************
 *   Dropdown Operations
 ***************************
 */
export const dropdownTranslations = () => {
  return http.request<Result>("get", "/api/auth/translations/dropdown");
};

/*
 ***************************
 *   Special Operations
 ***************************
 */
export const getTranslationGroups = () => {
  return http.request<Result>("get", "/api/auth/translations/groups");
};

export const syncTranslations = (data: { languageCode: string }) => {
  return http.request<Result>("post", "/api/auth/translations/sync", {
    data
  });
};

export const exportTranslations = (params?: object) => {
  return http.request<Result>("get", "/api/auth/translations/export", {
    params,
    responseType: "blob"
  });
};

export const importTranslations = (data: FormData) => {
  return http.request<Result>("post", "/api/auth/translations/import", {
    data,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
