import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import { h } from "vue";
import { capitalized } from "@/utils/helpers";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "key",
    align: "left",
    sortable: true,
    minWidth: 200,
    headerRenderer: () => $t("Translation Key"),
    cellRenderer: ({ row }) => h("span", { class: "font-mono text-sm" }, row.key)
  },
  {
    prop: "languageCode",
    align: "center",
    sortable: true,
    width: 100,
    headerRenderer: () => $t("Language"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: "primary",
          size: "small"
        },
        () => row.languageCode?.toUpperCase()
      );
    }
  },
  {
    prop: "value",
    align: "left",
    sortable: true,
    minWidth: 250,
    headerRenderer: () => $t("Translation Value"),
      cellRenderer: ({ row }) => {
      const displayValue = row.value && row.value.length > 50
        ? `${row.value.substring(0, 50)}...`
        : row.value;
      return h("span", { class: "truncate" }, displayValue);
    }
  },
  {
    prop: "group",
    align: "center",
    sortable: true,
    width: 120,
    headerRenderer: () => $t("Group"),
    cellRenderer: ({ row }) => {
      return row.group
        ? h(ElTag, { size: "small", type: "info" }, () => row.group)
        : h("span", { class: "text-gray-400" }, "-");
    }
  },
  {
    prop: "isPlural",
    align: "center",
    sortable: true,
    width: 80,
    headerRenderer: () => $t("Plural"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.isPlural ? "warning" : "info",
          size: "small"
        },
        () => row.isPlural ? $t("Yes") : $t("No")
      );
    }
  },
  {
    prop: "status",
    align: "center",
    sortable: true,
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.status === "active" ? "success" : "danger",
          size: "small"
        },
        () => $t(capitalized(row.status)).toUpperCase()
      );
    }
  },
  {
    prop: "createdAt",
    width: 160,
    sortable: true,
    headerRenderer: () => $t("Created at"),
    formatter: ({ createdAt }) =>
      createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm") : "-"
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
