import { reactive, ref } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import {
  getTranslations,
  deleteTranslationById,
  bulkDeleteTranslations,
  destroyTranslationById,
  bulkDestroyTranslations,
  restoreTranslationById,
  bulkRestoreTranslations,
  createTranslation,
  updateTranslationById
} from "@/views/translation/utils/auth-api";
import type { TranslationFilterProps } from "@/views/translation/utils/type";

export function useTranslationHook() {
  /*
   ***************************
   *   Data/State Management
   ***************************
   */
  const loading = ref(false);
  const filterRef = ref<TranslationFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const groups = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({
    status: "active",
    isActive: true
  });
  const translationFormRef = ref();

  /*
   ***************************
   *   API Data Fetching
   ***************************
   */
  const fnGetTranslations = async () => {
    loading.value = true;
    try {
      const response = await getTranslations(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;

      console.log("Re vale***************>", records.value);
    } catch (e) {
      console.error("Get Translation error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };



  /*
   ***************************
   *   Table Event Handlers
   ***************************
   */
  const fnHandlePageChange = (page: number) => {
    pagination.currentPage = page;
    fnGetTranslations();
  };

  const fnHandleSelectionChange = (selection: any[]) => {
    multipleSelection.value = selection;
  };

  const fnHandleSortChange = ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetTranslations();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetTranslations();
  };

  /*
   ***************************
   *   Form Related Operations
   ***************************
   */
  const handleSubmit = async (values: FieldValues) => {
    try {
      if (values.id) {
        await updateTranslationById(Number(values.id), values);
        message($t("Update successful"), { type: "success" });
      } else {
        await createTranslation(values);
        message($t("Create successful"), { type: "success" });
      }
      drawerVisible.value = false;
      fnGetTranslations();
    } catch (e) {
      message(
        e.response?.data?.message || e?.message || $t("Operation failed"),
        {
          type: "error"
        }
      );
    }
  };

  const handleFilter = (values?: any) => {
    if (values) {
      filterRef.value = values;
    }
    pagination.currentPage = 1;
    fnGetTranslations();
    filterVisible.value = false;
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */
  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this translation?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await deleteTranslationById(row.id);
      message($t("Delete successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message ||
            error?.message ||
            $t("Delete failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected translations?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      await bulkDeleteTranslations({ ids });
      message($t("Delete successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message ||
            error?.message ||
            $t("Delete failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  // Destroy operations
  const handleDestroy = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this translation?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await destroyTranslationById(row.id);
      message($t("Destroy successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message ||
            error?.message ||
            $t("Destroy failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  const handleBulkDestroy = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to destroy"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete selected translations?"
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      await bulkDestroyTranslations({ ids });
      message($t("Destroy successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message ||
            error?.message ||
            $t("Destroy failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  // Restore operations
  const handleRestore = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this translation?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );
      await restoreTranslationById(row.id);
      message($t("Restore successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message ||
            error?.message ||
            $t("Restore failed"),
          {
            type: "error"
          }
        );
      }
    }
  };

  const handleBulkRestore = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected translations?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      await bulkRestoreTranslations({ ids });
      message($t("Restore successful"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message ||
            error?.message ||
            $t("Restore failed"),
          {
            type: "error"
          }
        );
      }
    }
  };



  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    groups,
    multipleSelection,
    // Event handlers
    handleBulkDelete,
    handleDelete,
    fnGetTranslations,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    // Form related
    filterVisible,
    drawerVisible,
    drawerValues,
    translationFormRef,
    handleSubmit,
    handleFilter,
    handleBulkDestroy,
    handleBulkRestore,
    handleDestroy,
    handleRestore
  };
}
