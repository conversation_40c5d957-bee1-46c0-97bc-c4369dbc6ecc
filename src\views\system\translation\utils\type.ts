export type FormItemProps = {
  id?: number | null;
  key?: string;
  languageCode?: string;
  value?: string;
  description?: string;
  group?: string;
  isPlural?: boolean;
  pluralForms?: Record<string, string>;
  status?: string;
  isActive?: boolean;
  createdBy?: number;
  updatedBy?: number;
};

export type TranslationFilterProps = {
  key?: string;
  languageCode?: string;
  value?: string;
  group?: string;
  isPlural?: boolean;
  status?: string;
  isActive?: boolean;
  createdBy?: number;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
