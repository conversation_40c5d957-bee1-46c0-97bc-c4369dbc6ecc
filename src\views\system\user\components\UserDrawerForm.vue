<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const formRef = ref();

const columns = computed((): PlusColumn[] => [
  {
    label: $t("Name"),
    prop: "name",
    valueType: "text",
    fieldProps: {
      placeholder: $t("Please enter name")
    },
    formItemProps: {
      rules: [
        {
          required: true,
          message: $t("Please enter name")
        }
      ]
    }
  },
  {
    label: $t("Email"),
    prop: "email",
    valueType: "text",
    fieldProps: {
      placeholder: $t("Please enter email")
    },
    formItemProps: {
      rules: [
        {
          required: true,
          message: $t("Please enter email")
        },
        {
          type: "email",
          message: $t("Please enter valid email")
        }
      ]
    }
  },
  {
    label: $t("Password"),
    prop: "password",
    valueType: "password",
    fieldProps: {
      placeholder: $t("Please enter password"),
      showPassword: true
    },
    formItemProps: {
      rules: [
        {
          required: true,
          message: $t("Please enter password")
        },
        {
          min: 6,
          message: $t("Password must be at least 6 characters")
        }
      ]
    },
    hideInForm: computed(() => !!formRef.value?.values?.id)
  },
  {
    label: $t("Confirm Password"),
    prop: "password_confirmation",
    valueType: "password",
    fieldProps: {
      placeholder: $t("Please confirm password"),
      showPassword: true
    },
    formItemProps: {
      rules: [
        {
          required: true,
          message: $t("Please confirm password")
        },
        {
          validator: (rule: any, value: string, callback: Function) => {
            if (value !== formRef.value?.values?.password) {
              callback(new Error($t("Passwords do not match")));
            } else {
              callback();
            }
          }
        }
      ]
    },
    hideInForm: computed(() => !!formRef.value?.values?.id)
  },
  {
    label: $t("Phone"),
    prop: "phone",
    valueType: "text",
    fieldProps: {
      placeholder: $t("Please enter phone number")
    }
  },
  {
    label: $t("Address"),
    prop: "address",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Please enter address"),
      rows: 3
    }
  },
  {
    label: $t("Status"),
    prop: "is_active",
    valueType: "switch",
    fieldProps: {
      activeText: $t("Active"),
      inactiveText: $t("Inactive")
    }
  }
]);

const handleSubmit = (values: FieldValues) => {
  emit("submit", values);
};

const resetForm = () => {
  formRef.value?.resetFields();
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :form="{
      columns,
      labelWidth: 120,
      labelPosition: 'right',
      hasFooter: true,
      footerAlign: 'right',
      hasReset: true,
      resetText: $t('Reset'),
      submitText: $t('Submit'),
      hasCancel: true,
      cancelText: $t('Cancel')
    }"
    :values="values"
    :title="values?.id ? $t('Edit User') : $t('Add User')"
    :width="600"
    @update:visible="emit('update:visible', $event)"
    @update:values="emit('update:values', $event)"
    @submit="handleSubmit"
  />
</template>
