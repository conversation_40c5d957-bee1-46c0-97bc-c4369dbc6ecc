<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const formRef = ref();

const columns = computed((): PlusColumn[] => [
  {
    label: $t("Name"),
    prop: "name",
    valueType: "text",
    fieldProps: {
      placeholder: $t("Please enter name")
    }
  },
  {
    label: $t("Email"),
    prop: "email",
    valueType: "text",
    fieldProps: {
      placeholder: $t("Please enter email")
    }
  },
  {
    label: $t("Status"),
    prop: "is_active",
    valueType: "select",
    options: [
      { label: $t("All"), value: "" },
      { label: $t("Active"), value: true },
      { label: $t("Inactive"), value: false }
    ],
    fieldProps: {
      placeholder: $t("Please select status")
    }
  },
  {
    label: $t("Trash Status"),
    prop: "isTrashed",
    valueType: "select",
    options: [
      { label: $t("Normal"), value: false },
      { label: $t("Trashed"), value: true }
    ],
    fieldProps: {
      placeholder: $t("Please select trash status")
    }
  }
]);

const handleSubmit = (values: FieldValues) => {
  emit("submit", values);
};

const handleReset = () => {
  emit("reset");
};

const resetForm = () => {
  formRef.value?.resetFields();
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :form="{
      columns,
      labelWidth: 120,
      labelPosition: 'right',
      hasFooter: true,
      footerAlign: 'right',
      hasReset: true,
      resetText: $t('Reset'),
      submitText: $t('Filter'),
      hasCancel: true,
      cancelText: $t('Cancel')
    }"
    :values="values"
    :title="$t('Filter Users')"
    :width="500"
    @update:visible="emit('update:visible', $event)"
    @update:values="emit('update:values', $event)"
    @submit="handleSubmit"
    @reset="handleReset"
  />
</template>
