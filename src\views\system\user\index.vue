<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useUserHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const UserDrawerForm = defineAsyncComponent(
  () => import("./components/UserDrawerForm.vue")
);

const UserFilterForm = defineAsyncComponent(
  () => import("./components/UserFilterForm.vue")
);

const tableRef = ref();
const {
  loading,
  filterRef,
  records,
  multipleSelection,
  filterVisible,
  drawerVisible,
  drawerValues,
  userFormRef,
  fnGetUsers,
  fnHandleSelectionChange,
  handleDelete,
  handleDestroy,
  handleRestore,
  handleBulkDelete,
  handleSubmit,
  handleFilter,
  handleReset
} = useUserHook();

const handleAdd = () => {
  drawerValues.value = {};
  drawerVisible.value = true;
};

const handleEdit = (row: any) => {
  drawerValues.value = clone(row);
  drawerVisible.value = true;
};

const handleSizeChange = (val: number) => {
  console.log(`${val} items per page`);
};

const handleCurrentChange = (val: number) => {
  console.log(`current page: ${val}`);
};

const handleSelectionChange = (val: any) => {
  fnHandleSelectionChange(val);
};

const onSearch = () => {
  fnGetUsers();
};

const resetForm = () => {
  handleReset();
};

const openDialog = () => {
  handleAdd();
};

const handleMenuClick = (command: string, row?: any) => {
  switch (command) {
    case "edit":
      handleEdit(row);
      break;
    case "delete":
      handleDelete(row);
      break;
    case "destroy":
      handleDestroy(row);
      break;
    case "restore":
      handleRestore(row);
      break;
  }
};
</script>

<template>
  <div class="main">
    <div
      v-if="!deviceDetection()"
      class="w-full h-full flex flex-col"
    >
      <PureTableBar
        class="w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        border
        :title="$t('User Management')"
        :columns="columns"
        :isExpandAll="false"
        :tableRef="tableRef?.getTableRef()"
        @refresh="fnGetUsers"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-button
            v-if="hasAuth('user:create')"
            type="primary"
            :icon="useRenderIcon('ep:plus')"
            @click="openDialog"
          >
            {{ $t("Add User") }}
          </el-button>
          <el-button
            v-if="hasAuth('user:delete') && multipleSelection.length > 0"
            type="danger"
            :icon="useRenderIcon('ep:delete')"
            @click="handleBulkDelete"
          >
            {{ $t("Bulk Delete") }}
          </el-button>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            border
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            row-key="id"
            alignWhole="center"
            showOverflowTooltip
            table-layout="auto"
            :loading="loading"
            :size="size"
            :data="records"
            :columns="dynamicColumns"
            :pagination="{
              total: records.length,
              pageSize: 10,
              currentPage: 1,
              background: true
            }"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @selection-change="handleSelectionChange"
            @page-size-change="handleSizeChange"
            @page-current-change="handleCurrentChange"
          >
            <template #operation="{ row }">
              <div class="flex items-center gap-2">
                <el-button
                  v-if="hasAuth('user:update')"
                  type="primary"
                  size="small"
                  :icon="useRenderIcon('ep:edit')"
                  @click="handleEdit(row)"
                >
                  {{ $t("Edit") }}
                </el-button>
                
                <el-dropdown
                  v-if="hasAuth('user:delete') || hasAuth('user:restore')"
                  @command="(command: string) => handleMenuClick(command, row)"
                >
                  <el-button
                    type="danger"
                    size="small"
                    :icon="useRenderIcon('ep:arrow-down')"
                  >
                    {{ $t("More") }}
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-if="hasAuth('user:delete') && !filterRef.isTrashed"
                        command="delete"
                        :icon="useRenderIcon('ep:delete')"
                      >
                        {{ $t("Delete") }}
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="hasAuth('user:delete') && filterRef.isTrashed"
                        command="destroy"
                        :icon="useRenderIcon('ep:delete')"
                      >
                        {{ $t("Destroy") }}
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="hasAuth('user:restore') && filterRef.isTrashed"
                        command="restore"
                        :icon="useRenderIcon('ep:refresh')"
                      >
                        {{ $t("Restore") }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <UserDrawerForm
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      ref="userFormRef"
      @submit="handleSubmit"
    />

    <UserFilterForm
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="() => { filterRef = { isTrashed: false }; fnGetUsers(); }"
    />
  </div>
</template>
