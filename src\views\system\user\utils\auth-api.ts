import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/system/user/utils/type";

/* ***************************
 * API Data Fetching
 *************************** */

export const getUsers = (params?: object) => {
  return http.request<Result>("get", "/api/auth/users", {
    params
  });
};

export const getUserById = (id: number) => {
  return http.request<Result>("get", `/api/auth/users/${id}`);
};

/* ***************************
 * API CRUD Operations
 *************************** */

export const createUser = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/users", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateUserById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/users/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/* ***************************
 * Delete handlers and actions
 *************************** */

export const deleteUser = (id: number) => {
  return http.request<Result>("delete", `/api/auth/users/${id}`);
};

export const bulkDeleteUsers = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/users/bulk-delete", {
    data
  });
};

/* ***************************
 * Destroy handlers and actions
 *************************** */

export const destroyUser = (id: number) => {
  return http.request<Result>("delete", `/api/auth/users/${id}/force-delete`);
};

export const bulkDestroyUsers = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/users/bulk-destroy", {
    data
  });
};

/* ***************************
 * Restore handlers and actions
 *************************** */

export const restoreUser = (id: number) => {
  return http.request<Result>("put", `/api/auth/users/${id}/restore`);
};

export const bulkRestoreUsers = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/users/bulk-restore", {
    data
  });
};

/* ***************************
 * Dropdown/Select Options
 *************************** */

export const dropdownUsers = () => {
  return http.request<Result>("get", "/api/auth/users/dropdown");
};
