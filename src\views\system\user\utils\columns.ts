import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "avatar",
    width: 80,
    headerRenderer: () => $t("Avatar"),
    cellRenderer: ({ row }) => (
      <div class="flex items-center justify-center">
        <el-avatar
          size={40}
          src={row.avatar}
          icon="User"
          class="border border-gray-200"
        />
      </div>
    )
  },
  {
    prop: "name",
    align: "left",
    sortable: "custom",
    width: 180,
    headerRenderer: () => $t("Name")
  },
  {
    prop: "email",
    align: "left",
    sortable: "custom",
    width: 200,
    headerRenderer: () => $t("Email")
  },
  {
    prop: "phone",
    align: "left",
    width: 150,
    headerRenderer: () => $t("Phone")
  },
  {
    prop: "is_active",
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => (
      <el-tag type={row.is_active ? "success" : "danger"}>
        {row.is_active ? $t("Active") : $t("Inactive")}
      </el-tag>
    )
  },
  {
    prop: "created_at",
    width: 180,
    sortable: "custom",
    headerRenderer: () => $t("Created At"),
    formatter: ({ created_at }) =>
      dayjs(created_at).format("YYYY-MM-DD HH:mm:ss")
  },
  {
    prop: "operation",
    fixed: "right",
    width: 160,
    slot: "operation",
    headerRenderer: () => $t("Operation")
  }
];
