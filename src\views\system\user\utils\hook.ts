import { reactive, ref, onMounted } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel } from "@/utils/helpers";
import type { UserFilterProps } from "./type";
import {
  getUsers,
  createUser,
  updateUserById,
  bulkDeleteUsers,
  deleteUser,
  destroyUser,
  bulkDestroyUsers,
  restoreUser,
  bulkRestoreUsers
} from "./auth-api";

export function useUserHook() {
  /* ***************************
   * Data/State Management
   *************************** */
  const loading = ref(false);
  let filterRef = reactive<UserFilterProps>({ isTrashed: false });
  const multipleSelection = ref([]);
  const records = ref([]);
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({});
  const userFormRef = ref();

  /* ***************************
   * API Data Fetching
   *************************** */
  const fnGetUsers = async () => {
    loading.value = true;
    try {
      const res = await getUsers(filterRef);
      records.value = useConvertKeyToCamel(res.data);
    } catch (error) {
      console.error("Error fetching users:", error);
      message($t("Failed to fetch data"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * API CRUD Operations
   *************************** */
  const fnHandleCreateUser = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createUser(formData);
      if (response.success) {
        message(response.message, { type: "success" });
        await fnGetUsers();
        return true;
      }
      message(response.message || $t("Create failed"), { type: "error" });
      return false;
    } catch (error) {
      console.error("Create user error:", error);
      message($t("Create failed"), { type: "error" });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateUser = async (id: number, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateUserById(id, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), { type: "success" });
        await fnGetUsers();
        return true;
      }
      message(response.message || $t("Update failed"), { type: "error" });
      return false;
    } catch (error) {
      console.error("Update user error:", error);
      message($t("Update failed"), { type: "error" });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Table Event Handlers
   *************************** */
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  /* ***************************
   * Delete handlers and actions
   *************************** */
  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      
      loading.value = true;
      const response = await deleteUser(row.id);
      if (response.success) {
        message(response.message || $t("Delete successful"), { type: "success" });
        await fnGetUsers();
      } else {
        message(response.message || $t("Delete failed"), { type: "error" });
      }
    } catch (error) {
      console.error("Delete user error:", error);
      message($t("Delete failed"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const handleDestroy = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      
      loading.value = true;
      const response = await destroyUser(row.id);
      if (response.success) {
        message(response.message || $t("Delete successful"), { type: "success" });
        await fnGetUsers();
      } else {
        message(response.message || $t("Delete failed"), { type: "error" });
      }
    } catch (error) {
      console.error("Destroy user error:", error);
      message($t("Delete failed"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const handleRestore = async (row: any) => {
    try {
      loading.value = true;
      const response = await restoreUser(row.id);
      if (response.success) {
        message(response.message || $t("Restore successful"), { type: "success" });
        await fnGetUsers();
      } else {
        message(response.message || $t("Restore failed"), { type: "error" });
      }
    } catch (error) {
      console.error("Restore user error:", error);
      message($t("Restore failed"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      loading.value = true;
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDeleteUsers({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), { type: "success" });
        await fnGetUsers();
      } else {
        message(response.message || $t("Delete failed"), { type: "error" });
      }
    } catch (error) {
      console.error("Bulk delete users error:", error);
      message($t("Delete failed"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Form Handlers
   *************************** */
  const handleSubmit = async (values: FieldValues) => {
    if (values.id != null) {
      await fnHandleUpdateUser(Number(values.id), values);
      return;
    }
    const success = await fnHandleCreateUser(values);
    if (success) {
      drawerValues.value = {};
      userFormRef.value?.resetForm();
    }
  };

  const handleFilter = async (values: UserFilterProps) => {
    filterRef = values;
    await fnGetUsers();
  };

  const handleReset = () => {
    filterRef = { isTrashed: false };
    fnGetUsers();
  };

  /* ***************************
   * Lifecycle
   *************************** */
  onMounted(() => {
    fnGetUsers();
  });

  /* ***************************
   * Return Hook Interface
   *************************** */
  return {
    loading,
    filterRef,
    records,
    multipleSelection,
    filterVisible,
    drawerVisible,
    drawerValues,
    userFormRef,
    fnGetUsers,
    fnHandleCreateUser,
    fnHandleUpdateUser,
    fnHandleSelectionChange,
    handleDelete,
    handleDestroy,
    handleRestore,
    handleBulkDelete,
    handleSubmit,
    handleFilter,
    handleReset
  };
}
