<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload Clear</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
</head>
<body>
    <div id="app">
        <div style="padding: 20px;">
            <h2>Test Upload Clear Functionality</h2>
            
            <div style="margin-bottom: 20px;">
                <h3>Before Fix (Simulated)</h3>
                <p>File list should remain after submit success</p>
                <el-upload
                    ref="uploadRefBefore"
                    v-model:file-list="fileListBefore"
                    action="#"
                    :auto-upload="false"
                    multiple
                >
                    <el-button type="primary">Click to upload</el-button>
                </el-upload>
                <div style="margin-top: 10px;">
                    <el-button @click="simulateSubmitBefore">Simulate Submit (Before Fix)</el-button>
                    <el-button @click="clearFilesBefore">Manual Clear</el-button>
                </div>
                <p>File count: {{ fileListBefore.length }}</p>
            </div>

            <div style="margin-bottom: 20px;">
                <h3>After Fix</h3>
                <p>File list should be cleared after submit success</p>
                <el-upload
                    ref="uploadRefAfter"
                    v-model:file-list="fileListAfter"
                    action="#"
                    :auto-upload="false"
                    multiple
                >
                    <el-button type="primary">Click to upload</el-button>
                </el-upload>
                <div style="margin-top: 10px;">
                    <el-button @click="simulateSubmitAfter">Simulate Submit (After Fix)</el-button>
                    <el-button @click="clearFilesAfter">Manual Clear</el-button>
                </div>
                <p>File count: {{ fileListAfter.length }}</p>
            </div>

            <div>
                <h3>Test Results</h3>
                <ul>
                    <li>Before fix: Files remain in list after submit</li>
                    <li>After fix: Files are cleared from list after submit</li>
                    <li>Both should clear when manual clear is clicked</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;
        const { ElUpload, ElButton, ElMessage } = ElementPlus;

        createApp({
            components: {
                ElUpload,
                ElButton
            },
            setup() {
                const uploadRefBefore = ref();
                const uploadRefAfter = ref();
                const fileListBefore = ref([]);
                const fileListAfter = ref([]);

                // Simulate the old behavior (before fix)
                const simulateSubmitBefore = () => {
                    ElMessage.success('Submit successful (Before Fix)');
                    // Old code: knowledgeFiles = [] (doesn't work)
                    // fileListBefore.value = []; // This line was missing
                    console.log('Before fix: Files not cleared, count:', fileListBefore.value.length);
                };

                // Simulate the new behavior (after fix)
                const simulateSubmitAfter = () => {
                    ElMessage.success('Submit successful (After Fix)');
                    // New code: properly clear files
                    fileListAfter.value = [];
                    if (uploadRefAfter.value) {
                        uploadRefAfter.value.clearFiles();
                    }
                    console.log('After fix: Files cleared, count:', fileListAfter.value.length);
                };

                const clearFilesBefore = () => {
                    fileListBefore.value = [];
                    if (uploadRefBefore.value) {
                        uploadRefBefore.value.clearFiles();
                    }
                };

                const clearFilesAfter = () => {
                    fileListAfter.value = [];
                    if (uploadRefAfter.value) {
                        uploadRefAfter.value.clearFiles();
                    }
                };

                return {
                    uploadRefBefore,
                    uploadRefAfter,
                    fileListBefore,
                    fileListAfter,
                    simulateSubmitBefore,
                    simulateSubmitAfter,
                    clearFilesBefore,
                    clearFilesAfter
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
